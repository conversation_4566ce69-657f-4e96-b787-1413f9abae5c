{"data_mtime": 1751813592, "dep_lines": [13, 14, 15, 16, 17, 57, 6, 7, 8, 57, 58, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 20, 10, 10, 10, 20, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["modules.screen_capture", "modules.image_recognition", "modules.path_finder", "modules.auto_clicker", "utils.helpers", "PIL.Image", "sys", "os", "time", "PIL", "numpy", "builtins", "_frozen_importlib", "_typeshed", "abc", "modules", "typing", "utils"], "hash": "7475b037ffb6f7cb274a626d5d1bb7fb44ec529e", "id": "demo", "ignore_all": false, "interface_hash": "7748b45b040c81c0a15d56dc4be056a22507e7dc", "mtime": 1751813589, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\db\\0617\\连连看辅助工具\\demo.py", "plugin_data": null, "size": 7976, "suppressed": [], "version_id": "1.15.0"}