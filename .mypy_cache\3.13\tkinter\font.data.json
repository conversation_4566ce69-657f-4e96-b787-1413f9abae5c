{".class": "MypyFile", "_fullname": "tkinter.font", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BOLD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "bold", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.font.BOLD", "name": "BOLD", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "bold"}, "type_ref": "builtins.str"}}}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Font": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tkinter.font.Font", "name": "Font", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tkinter.font.Font", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tkinter.font", "mro": ["tkinter.font.Font", "builtins.object"], "names": {".class": "SymbolTable", "__del__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tkinter.font.Font.__del__", "name": "__del__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["tkinter.font.Font"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__del__ of Font", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tkinter.font.Font.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["tkinter.font.Font", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of Font", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "tkinter.font.Font.__getitem__", "name": "__getitem__", "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "option"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "family"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "option"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "size"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "option"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "weight"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "normal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bold"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "option"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "slant"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "roman"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "italic"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "option"], "arg_types": ["tkinter.font.Font", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "underline"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "overstrike"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "option"], "arg_types": ["tkinter.font.Font", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "tkinter.font.Font.__hash__", "name": "__hash__", "type": {".class": "NoneType"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "root", "font", "name", "exists", "family", "size", "weight", "slant", "underline", "overstrike"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tkinter.font.Font.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "root", "font", "name", "exists", "family", "size", "weight", "slant", "underline", "overstrike"], "arg_types": ["tkinter.font.Font", {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "tkinter.font._FontDescription"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.str", "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "normal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bold"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "roman"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "italic"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Font", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__setitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tkinter.font.Font.__setitem__", "name": "__setitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["tkinter.font.Font", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setitem__ of Font", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "actual": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "tkinter.font.Font.actual", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "option", "displayof"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "tkinter.font.Font.actual", "name": "actual", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "option", "displayof"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "family"}, {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "actual of Font", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tkinter.font.Font.actual", "name": "actual", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "option", "displayof"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "family"}, {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "actual of Font", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "option", "displayof"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "tkinter.font.Font.actual", "name": "actual", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "option", "displayof"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "size"}, {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "actual of Font", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tkinter.font.Font.actual", "name": "actual", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "option", "displayof"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "size"}, {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "actual of Font", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "option", "displayof"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "tkinter.font.Font.actual", "name": "actual", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "option", "displayof"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "weight"}, {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "actual of Font", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "normal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bold"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tkinter.font.Font.actual", "name": "actual", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "option", "displayof"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "weight"}, {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "actual of Font", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "normal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bold"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "option", "displayof"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "tkinter.font.Font.actual", "name": "actual", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "option", "displayof"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "slant"}, {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "actual of Font", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "roman"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "italic"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tkinter.font.Font.actual", "name": "actual", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "option", "displayof"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "slant"}, {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "actual of Font", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "roman"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "italic"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "option", "displayof"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "tkinter.font.Font.actual", "name": "actual", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "option", "displayof"], "arg_types": ["tkinter.font.Font", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "underline"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "overstrike"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "actual of Font", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tkinter.font.Font.actual", "name": "actual", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "option", "displayof"], "arg_types": ["tkinter.font.Font", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "underline"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "overstrike"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "actual of Font", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "option", "displayof"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "tkinter.font.Font.actual", "name": "actual", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "option", "displayof"], "arg_types": ["tkinter.font.Font", {".class": "NoneType"}, {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "actual of Font", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "tkinter.font._FontDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tkinter.font.Font.actual", "name": "actual", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "option", "displayof"], "arg_types": ["tkinter.font.Font", {".class": "NoneType"}, {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "actual of Font", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "tkinter.font._FontDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "displayof"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "tkinter.font.Font.actual", "name": "actual", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "displayof"], "arg_types": ["tkinter.font.Font", {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "actual of Font", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "tkinter.font._FontDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tkinter.font.Font.actual", "name": "actual", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "displayof"], "arg_types": ["tkinter.font.Font", {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "actual of Font", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "tkinter.font._FontDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "option", "displayof"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "family"}, {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "actual of Font", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "option", "displayof"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "size"}, {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "actual of Font", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "option", "displayof"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "weight"}, {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "actual of Font", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "normal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bold"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "option", "displayof"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "slant"}, {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "actual of Font", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "roman"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "italic"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "option", "displayof"], "arg_types": ["tkinter.font.Font", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "underline"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "overstrike"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "actual of Font", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "option", "displayof"], "arg_types": ["tkinter.font.Font", {".class": "NoneType"}, {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "actual of Font", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "tkinter.font._FontDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "displayof"], "arg_types": ["tkinter.font.Font", {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "actual of Font", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "tkinter.font._FontDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "cget": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "tkinter.font.Font.cget", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "option"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "tkinter.font.Font.cget", "name": "cget", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "option"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "family"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cget of Font", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tkinter.font.Font.cget", "name": "cget", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "option"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "family"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cget of Font", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "option"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "tkinter.font.Font.cget", "name": "cget", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "option"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "size"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cget of Font", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tkinter.font.Font.cget", "name": "cget", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "option"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "size"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cget of Font", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "option"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "tkinter.font.Font.cget", "name": "cget", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "option"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "weight"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cget of Font", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "normal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bold"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tkinter.font.Font.cget", "name": "cget", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "option"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "weight"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cget of Font", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "normal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bold"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "option"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "tkinter.font.Font.cget", "name": "cget", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "option"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "slant"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cget of Font", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "roman"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "italic"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tkinter.font.Font.cget", "name": "cget", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "option"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "slant"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cget of Font", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "roman"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "italic"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "option"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "tkinter.font.Font.cget", "name": "cget", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "option"], "arg_types": ["tkinter.font.Font", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "underline"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "overstrike"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cget of Font", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tkinter.font.Font.cget", "name": "cget", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "option"], "arg_types": ["tkinter.font.Font", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "underline"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "overstrike"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cget of Font", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "option"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "tkinter.font.Font.cget", "name": "cget", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "option"], "arg_types": ["tkinter.font.Font", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cget of Font", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tkinter.font.Font.cget", "name": "cget", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "option"], "arg_types": ["tkinter.font.Font", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cget of Font", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "option"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "family"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cget of Font", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "option"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "size"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cget of Font", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "option"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "weight"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cget of Font", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "normal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bold"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "option"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "slant"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cget of Font", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "roman"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "italic"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "option"], "arg_types": ["tkinter.font.Font", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "underline"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "overstrike"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cget of Font", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "option"], "arg_types": ["tkinter.font.Font", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cget of Font", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "family", "size", "weight", "slant", "underline", "overstrike"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tkinter.font.Font.config", "name": "config", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "family", "size", "weight", "slant", "underline", "overstrike"], "arg_types": ["tkinter.font.Font", "builtins.str", "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "normal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bold"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "roman"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "italic"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "config of Font", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "tkinter.font._FontDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "configure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "tkinter.font.Font.configure", "name": "configure", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "family", "size", "weight", "slant", "underline", "overstrike"], "arg_types": ["tkinter.font.Font", "builtins.str", "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "normal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bold"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "roman"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "italic"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "tkinter.font._FontDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tkinter.font.Font.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tkinter.font.Font"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of <PERSON><PERSON>", "ret_type": "tkinter.font.Font", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "counter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "tkinter.font.Font.counter", "name": "counter", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "itertools.count"}}}, "delete_font": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tkinter.font.Font.delete_font", "name": "delete_font", "type": "builtins.bool"}}, "measure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "text", "displayof"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tkinter.font.Font.measure", "name": "measure", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "text", "displayof"], "arg_types": ["tkinter.font.Font", "builtins.str", {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "measure of Font", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "metrics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "tkinter.font.Font.metrics", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": [null, null, "displayof"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "tkinter.font.Font.metrics", "name": "metrics", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": [null, null, "displayof"], "arg_types": ["tkinter.font.Font", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "ascent"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "descent"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "linespace"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "metrics of Font", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tkinter.font.Font.metrics", "name": "metrics", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": [null, null, "displayof"], "arg_types": ["tkinter.font.Font", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "ascent"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "descent"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "linespace"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "metrics of Font", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": [null, null, "displayof"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "tkinter.font.Font.metrics", "name": "metrics", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": [null, null, "displayof"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "fixed"}, {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "metrics of Font", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tkinter.font.Font.metrics", "name": "metrics", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": [null, null, "displayof"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "fixed"}, {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "metrics of Font", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "displayof"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "tkinter.font.Font.metrics", "name": "metrics", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "displayof"], "arg_types": ["tkinter.font.Font", {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "metrics of Font", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "tkinter.font._MetricsDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tkinter.font.Font.metrics", "name": "metrics", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "displayof"], "arg_types": ["tkinter.font.Font", {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "metrics of Font", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "tkinter.font._MetricsDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": [null, null, "displayof"], "arg_types": ["tkinter.font.Font", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "ascent"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "descent"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "linespace"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "metrics of Font", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": [null, null, "displayof"], "arg_types": ["tkinter.font.Font", {".class": "LiteralType", "fallback": "builtins.str", "value": "fixed"}, {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "metrics of Font", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "displayof"], "arg_types": ["tkinter.font.Font", {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "metrics of Font", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "tkinter.font._MetricsDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tkinter.font.Font.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tkinter.font.Font.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tkinter.font.Font", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ITALIC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "italic", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.font.ITALIC", "name": "ITALIC", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "italic"}, "type_ref": "builtins.str"}}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NORMAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "normal", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.font.NORMAL", "name": "NORMAL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "normal"}, "type_ref": "builtins.str"}}}, "ROMAN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "roman", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.font.ROMAN", "name": "ROMAN", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "roman"}, "type_ref": "builtins.str"}}}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Unpack": {".class": "SymbolTableNode", "cross_ref": "typing.Unpack", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FontDescription": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "tkinter.font._FontDescription", "line": 16, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", "tkinter.font.Font", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int", {".class": "UnpackType", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "_tkinter.Tcl_Obj"], "uses_pep604_syntax": true}}}, "_FontDict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tkinter.font._FontDict", "name": "_FontDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tkinter.font._FontDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tkinter.font", "mro": ["tkinter.font._FontDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["family", "builtins.str"], ["size", "builtins.int"], ["weight", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "normal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bold"}], "uses_pep604_syntax": false}], ["slant", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "roman"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "italic"}], "uses_pep604_syntax": false}], ["underline", "builtins.bool"], ["overstrike", "builtins.bool"]], "readonly_keys": [], "required_keys": ["family", "overstrike", "size", "slant", "underline", "weight"]}}}, "_MetricsDict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tkinter.font._MetricsDict", "name": "_MetricsDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tkinter.font._MetricsDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tkinter.font", "mro": ["tkinter.font._MetricsDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["ascent", "builtins.int"], ["descent", "builtins.int"], ["linespace", "builtins.int"], ["fixed", "builtins.bool"]], "readonly_keys": [], "required_keys": ["ascent", "descent", "fixed", "linespace"]}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "tkinter.font.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tkinter.font.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tkinter.font.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tkinter.font.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tkinter.font.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tkinter.font.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tkinter.font.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_tkinter": {".class": "SymbolTableNode", "cross_ref": "_tkinter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "families": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1], "arg_names": ["root", "displayof"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tkinter.font.families", "name": "families", "type": {".class": "CallableType", "arg_kinds": [1, 1], "arg_names": ["root", "displayof"], "arg_types": [{".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "families", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef", "module_hidden": true, "module_public": false}, "names": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["root"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tkinter.font.names", "name": "names", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["root"], "arg_types": [{".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "names", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "nametofont": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["name", "root"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tkinter.font.nametofont", "name": "nametofont", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["name", "root"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["tkinter.Misc", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nametofont", "ret_type": "tkinter.font.Font", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "tkinter": {".class": "SymbolTableNode", "cross_ref": "tkinter", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\tkinter\\font.pyi"}