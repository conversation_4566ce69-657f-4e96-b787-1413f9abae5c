{".class": "MypyFile", "_fullname": "modules.screen_capture", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Image": {".class": "SymbolTableNode", "cross_ref": "PIL.Image", "kind": "Gdef"}, "ImageGrab": {".class": "SymbolTableNode", "cross_ref": "PIL.ImageGrab", "kind": "Gdef"}, "ScreenCapture": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "modules.screen_capture.ScreenCapture", "name": "ScreenCapture", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "modules.screen_capture.ScreenCapture", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "modules.screen_capture", "mro": ["modules.screen_capture.ScreenCapture", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "modules.screen_capture.ScreenCapture.__init__", "name": "__init__", "type": null}}, "calculate_grid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "game_image"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "modules.screen_capture.ScreenCapture.calculate_grid", "name": "calculate_grid", "type": null}}, "capture_screen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "region"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "modules.screen_capture.ScreenCapture.capture_screen", "name": "capture_screen", "type": null}}, "cell_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "modules.screen_capture.ScreenCapture.cell_size", "name": "cell_size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "detect_game_region": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "screenshot"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "modules.screen_capture.ScreenCapture.detect_game_region", "name": "detect_game_region", "type": null}}, "game_region": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "modules.screen_capture.ScreenCapture.game_region", "name": "game_region", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_cell_image": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "row", "col", "game_image"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "modules.screen_capture.ScreenCapture.get_cell_image", "name": "get_cell_image", "type": null}}, "get_grid_center": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "row", "col"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "modules.screen_capture.ScreenCapture.get_grid_center", "name": "get_grid_center", "type": null}}, "grid_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "modules.screen_capture.ScreenCapture.grid_size", "name": "grid_size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "preprocess_image": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "image"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "modules.screen_capture.ScreenCapture.preprocess_image", "name": "preprocess_image", "type": null}}, "save_debug_image": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "image", "filename"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "modules.screen_capture.ScreenCapture.save_debug_image", "name": "save_debug_image", "type": null}}, "set_game_region": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "x", "y", "width", "height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "modules.screen_capture.ScreenCapture.set_game_region", "name": "set_game_region", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "modules.screen_capture.ScreenCapture.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "modules.screen_capture.ScreenCapture", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "modules.screen_capture.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "modules.screen_capture.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "modules.screen_capture.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "modules.screen_capture.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "modules.screen_capture.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "modules.screen_capture.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "capture": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "modules.screen_capture.capture", "name": "capture", "type": "modules.screen_capture.ScreenCapture"}}, "cell_image": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "modules.screen_capture.cell_image", "name": "cell_image", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "cell_size": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "modules.screen_capture.cell_size", "name": "cell_size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "col": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_index_var", "is_inferred"], "fullname": "modules.screen_capture.col", "name": "col", "type": "builtins.int"}}, "cv2": {".class": "SymbolTableNode", "cross_ref": "cv2", "kind": "Gdef"}, "game_image": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "modules.screen_capture.game_image", "name": "game_image", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "game_region": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "modules.screen_capture.game_region", "name": "game_region", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "pyautogui": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "modules.screen_capture.pyautogui", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": {".class": "AnyType", "missing_import_name": "modules.screen_capture.pyautogui", "source_any": null, "type_of_any": 3}}}, "row": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_index_var", "is_inferred"], "fullname": "modules.screen_capture.row", "name": "row", "type": "builtins.int"}}, "screenshot": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "modules.screen_capture.screenshot", "name": "screenshot", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}}, "path": "E:\\db\\0617\\连连看辅助工具\\modules\\screen_capture.py"}