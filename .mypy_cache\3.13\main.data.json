{".class": "MypyFile", "_fullname": "main", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AutoClicker": {".class": "SymbolTableNode", "cross_ref": "modules.auto_clicker.AutoClicker", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "GameAssistant": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "main.GameAssistant", "name": "GameAssistant", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "main.GameAssistant", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "main", "mro": ["main.GameAssistant", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.GameAssistant.__init__", "name": "__init__", "type": null}}, "auto_clicker": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.GameAssistant.auto_clicker", "name": "auto_clicker", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.GameAssistant.config", "name": "config", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "configure_modules": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.GameAssistant.configure_modules", "name": "configure_modules", "type": null}}, "current_grid": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.GameAssistant.current_grid", "name": "current_grid", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "detect_game_region": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.GameAssistant.detect_game_region", "name": "detect_game_region", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["main.GameAssistant"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detect_game_region of GameAssistant", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_and_execute_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.GameAssistant.find_and_execute_match", "name": "find_and_execute_match", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["main.GameAssistant"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_and_execute_match of GameAssistant", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "game_region": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.GameAssistant.game_region", "name": "game_region", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.GameAssistant.get_status", "name": "get_status", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["main.GameAssistant"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_status of GameAssistant", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gui": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.GameAssistant.gui", "name": "gui", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "image_recognition": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.GameAssistant.image_recognition", "name": "image_recognition", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "is_running": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.GameAssistant.is_running", "name": "is_running", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "learn_patterns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.GameAssistant.learn_patterns", "name": "learn_patterns", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["main.GameAssistant"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "learn_patterns of GameAssistant", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "path_finder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.GameAssistant.path_finder", "name": "path_finder", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "patterns_learned": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.GameAssistant.patterns_learned", "name": "patterns_learned", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "performance_monitor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.GameAssistant.performance_monitor", "name": "performance_monitor", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "run_auto_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.GameAssistant.run_auto_mode", "name": "run_auto_mode", "type": null}}, "scan_current_grid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.GameAssistant.scan_current_grid", "name": "scan_current_grid", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["main.GameAssistant"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scan_current_grid of GameAssistant", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "screen_capture": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "main.GameAssistant.screen_capture", "name": "screen_capture", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.GameAssistant.start", "name": "start", "type": null}}, "stop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.GameAssistant.stop", "name": "stop", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "main.GameAssistant.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "main.GameAssistant", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ImageRecognition": {".class": "SymbolTableNode", "cross_ref": "modules.image_recognition.ImageRecognition", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MainWindow": {".class": "SymbolTableNode", "cross_ref": "gui.main_window.MainWindow", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PathFinder": {".class": "SymbolTableNode", "cross_ref": "modules.path_finder.PathFinder", "kind": "Gdef"}, "ScreenCapture": {".class": "SymbolTableNode", "cross_ref": "modules.screen_capture.ScreenCapture", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "main.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "main.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "main.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "main.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "main.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "main.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "calculate_distance": {".class": "SymbolTableNode", "cross_ref": "utils.helpers.calculate_distance", "kind": "Gdef"}, "calculate_grid_metrics": {".class": "SymbolTableNode", "cross_ref": "utils.helpers.calculate_grid_metrics", "kind": "Gdef"}, "clamp": {".class": "SymbolTableNode", "cross_ref": "utils.helpers.clamp", "kind": "Gdef"}, "cleanup_old_files": {".class": "SymbolTableNode", "cross_ref": "utils.helpers.cleanup_old_files", "kind": "Gdef"}, "config": {".class": "SymbolTableNode", "cross_ref": "utils.helpers.config", "kind": "Gdef"}, "create_debug_directory": {".class": "SymbolTableNode", "cross_ref": "utils.helpers.create_debug_directory", "kind": "Gdef"}, "create_performance_monitor": {".class": "SymbolTableNode", "cross_ref": "utils.helpers.create_performance_monitor", "kind": "Gdef"}, "format_region": {".class": "SymbolTableNode", "cross_ref": "utils.helpers.format_region", "kind": "Gdef"}, "format_time": {".class": "SymbolTableNode", "cross_ref": "utils.helpers.format_time", "kind": "Gdef"}, "generate_timestamp": {".class": "SymbolTableNode", "cross_ref": "utils.helpers.generate_timestamp", "kind": "Gdef"}, "get_performance_stats": {".class": "SymbolTableNode", "cross_ref": "utils.helpers.get_performance_stats", "kind": "Gdef"}, "get_screen_info": {".class": "SymbolTableNode", "cross_ref": "utils.helpers.get_screen_info", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "load_config": {".class": "SymbolTableNode", "cross_ref": "utils.helpers.load_config", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "main.main", "name": "main", "type": null}}, "monitor": {".class": "SymbolTableNode", "cross_ref": "utils.helpers.monitor", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "parse_region_string": {".class": "SymbolTableNode", "cross_ref": "utils.helpers.parse_region_string", "kind": "Gdef"}, "safe_divide": {".class": "SymbolTableNode", "cross_ref": "utils.helpers.safe_divide", "kind": "Gdef"}, "save_config": {".class": "SymbolTableNode", "cross_ref": "utils.helpers.save_config", "kind": "Gdef"}, "screen_info": {".class": "SymbolTableNode", "cross_ref": "utils.helpers.screen_info", "kind": "Gdef"}, "setup_logging": {".class": "SymbolTableNode", "cross_ref": "utils.helpers.setup_logging", "kind": "Gdef"}, "stats": {".class": "SymbolTableNode", "cross_ref": "utils.helpers.stats", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "update_performance_monitor": {".class": "SymbolTableNode", "cross_ref": "utils.helpers.update_performance_monitor", "kind": "Gdef"}, "validate_grid_position": {".class": "SymbolTableNode", "cross_ref": "utils.helpers.validate_grid_position", "kind": "Gdef"}}, "path": "E:\\db\\0617\\连连看辅助工具\\main.py"}