"""
连连看辅助工具启动脚本
提供更好的用户体验和错误处理
"""

import sys
import os
import traceback

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_dependencies():
    """检查依赖包是否安装"""
    print("检查依赖包...")
    
    required_packages = [
        ('cv2', 'opencv-python'),
        ('PIL', 'pillow'),
        ('pyautogui', 'pyautogui'),
        ('numpy', 'numpy'),
        ('matplotlib', 'matplotlib')
    ]
    
    missing_packages = []
    
    for import_name, package_name in required_packages:
        try:
            __import__(import_name)
            print(f"✓ {package_name}")
        except ImportError:
            print(f"✗ {package_name} - 未安装")
            missing_packages.append(package_name)
    
    if missing_packages:
        print(f"\n缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✓ 所有依赖包已安装")
    return True

def check_modules():
    """检查自定义模块是否正常"""
    print("\n检查程序模块...")
    
    modules = [
        ('modules.screen_capture', '屏幕截图模块'),
        ('modules.image_recognition', '图像识别模块'),
        ('modules.path_finder', '路径查找模块'),
        ('modules.auto_clicker', '自动点击模块'),
        ('gui.main_window', '用户界面模块'),
        ('utils.helpers', '工具函数模块')
    ]
    
    for module_name, display_name in modules:
        try:
            __import__(module_name)
            print(f"✓ {display_name}")
        except Exception as e:
            print(f"✗ {display_name} - 错误: {e}")
            return False
    
    print("✓ 所有程序模块正常")
    return True

def run_quick_test():
    """运行快速测试"""
    print("\n运行快速测试...")
    
    try:
        from modules.screen_capture import ScreenCapture
        from modules.image_recognition import ImageRecognition
        from modules.path_finder import PathFinder
        from modules.auto_clicker import AutoClicker
        from utils.helpers import load_config, get_screen_info
        
        # 测试配置加载
        config = load_config()
        print(f"✓ 配置加载成功 ({len(config)} 项)")
        
        # 测试屏幕信息
        screen_info = get_screen_info()
        print(f"✓ 屏幕信息: {screen_info['width']}x{screen_info['height']}")
        
        # 测试模块初始化
        capture = ScreenCapture()
        recognition = ImageRecognition()
        path_finder = PathFinder()
        clicker = AutoClicker()
        print("✓ 核心模块初始化成功")
        
        # 测试截图
        screenshot = capture.capture_screen()
        if screenshot:
            print(f"✓ 截图测试成功: {screenshot.size}")
        else:
            print("⚠ 截图测试失败")
            return False
        
        print("✓ 快速测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 快速测试失败: {e}")
        traceback.print_exc()
        return False

def show_usage_tips():
    """显示使用提示"""
    print("\n" + "="*60)
    print("🎮 连连看辅助工具使用指南")
    print("="*60)
    print("1. 打开连连看游戏，确保游戏窗口完全可见")
    print("2. 点击'检测游戏区域'按钮，自动识别游戏界面")
    print("3. 点击'学习图案'按钮，让程序学习游戏中的图案")
    print("4. 点击'开始辅助'按钮，开始自动游戏")
    print("5. 观察日志输出了解运行状态")
    print("\n💡 提示:")
    print("- 将鼠标移到屏幕左上角可紧急停止")
    print("- 建议先在简单关卡测试效果")
    print("- 可以调整参数以适应不同游戏")
    print("- 启用人性化操作避免被检测")
    print("\n⚠️ 注意:")
    print("- 仅在允许使用辅助工具的游戏中使用")
    print("- 遵守游戏使用条款和相关法律法规")
    print("="*60)

def main():
    """主函数"""
    print("🎯 连连看游戏辅助工具 v1.0")
    print("正在启动...")
    
    # 检查依赖
    if not check_dependencies():
        input("\n按回车键退出...")
        return
    
    # 检查模块
    if not check_modules():
        input("\n按回车键退出...")
        return
    
    # 运行测试
    if not run_quick_test():
        print("\n⚠️ 快速测试失败，程序可能无法正常工作")
        choice = input("是否继续启动? (y/N): ").lower()
        if choice != 'y':
            return
    
    # 显示使用提示
    show_usage_tips()
    
    try:
        print("\n🚀 启动图形界面...")
        
        # 导入并启动主程序
        from main import GameAssistant
        from gui.main_window import MainWindow
        
        # 创建游戏辅助器
        assistant = GameAssistant()
        
        # 创建GUI界面
        gui = MainWindow()
        gui.game_assistant = assistant
        
        print("✓ 程序启动成功！")
        print("📱 图形界面已打开，请在界面中操作")
        
        # 运行GUI
        gui.run()
        
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序运行错误: {e}")
        traceback.print_exc()
        input("\n按回车键退出...")
    finally:
        print("👋 程序已退出")

if __name__ == "__main__":
    main()
