"""
连连看游戏辅助工具主程序
整合所有模块，提供完整的游戏辅助功能
"""

import sys
import os
import time
import threading
from typing import Optional, Tuple, List

# 添加项目路径到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入自定义模块
from modules.screen_capture import ScreenCapture
from modules.image_recognition import ImageRecognition
from modules.path_finder import PathFinder
from modules.auto_clicker import AutoClicker
from gui.main_window import MainWindow
from utils.helpers import (load_config, save_config, setup_logging,
                          get_screen_info, format_region,
                          create_performance_monitor, update_performance_monitor,
                          get_performance_stats)
import logging


class GameAssistant:
    """连连看游戏辅助主类"""
    
    def __init__(self):
        """初始化游戏辅助器"""
        # 加载配置
        self.config = load_config()
        
        # 初始化各个模块
        self.screen_capture = ScreenCapture()
        self.image_recognition = ImageRecognition()
        self.path_finder = PathFinder(
            grid_rows=self.config['grid_rows'],
            grid_cols=self.config['grid_cols']
        )
        self.auto_clicker = AutoClicker()
        
        # 配置模块参数
        self.configure_modules()
        
        # 状态变量
        self.is_running = False
        self.game_region = None
        self.current_grid = None
        self.patterns_learned = False
        
        # 性能监控
        self.performance_monitor = create_performance_monitor()
        
        # GUI界面
        self.gui = None
        
        # 设置日志
        setup_logging()
        
    def configure_modules(self):
        """配置各个模块的参数"""
        # 配置自动点击器
        self.auto_clicker.set_click_delay(self.config['click_delay'])
        self.auto_clicker.set_move_duration(self.config['move_duration'])
        self.auto_clicker.enable_human_like_behavior(self.config['enable_randomization'])
        
        # 配置图像识别
        self.image_recognition.similarity_threshold = self.config['similarity_threshold']
        
        # 配置屏幕截图
        self.screen_capture.grid_size = (self.config['grid_cols'], self.config['grid_rows'])
        
        logging.info("模块配置完成")
    
    def detect_game_region(self) -> Optional[Tuple[int, int, int, int]]:
        """
        检测游戏区域
        
        Returns:
            tuple: 游戏区域坐标 (x, y, width, height)
        """
        try:
            logging.info("开始检测游戏区域...")
            
            # 截取全屏
            screenshot = self.screen_capture.capture_screen()
            if not screenshot:
                logging.error("截图失败")
                return None
            
            # 自动检测游戏区域
            if self.config['auto_detect_region']:
                region = self.screen_capture.detect_game_region(screenshot)
                if region:
                    self.game_region = region
                    self.screen_capture.set_game_region(*region)
                    logging.info(f"自动检测到游戏区域: {format_region(region)}")
                    return region
            
            # 如果自动检测失败，使用默认区域
            screen_info = get_screen_info()
            default_region = (
                screen_info['width'] // 4,
                screen_info['height'] // 8,
                screen_info['width'] // 2,
                screen_info['height'] * 3 // 4
            )
            
            self.game_region = default_region
            self.screen_capture.set_game_region(*default_region)
            logging.warning(f"使用默认游戏区域: {format_region(default_region)}")
            
            return default_region
            
        except Exception as e:
            logging.error(f"检测游戏区域失败: {e}")
            return None
    
    def learn_patterns(self) -> bool:
        """
        学习游戏中的图案
        
        Returns:
            bool: 是否成功学习图案
        """
        try:
            if not self.game_region:
                logging.error("请先检测游戏区域")
                return False
            
            logging.info("开始学习图案...")
            
            # 截取游戏区域
            game_image = self.screen_capture.capture_screen(self.game_region)
            if not game_image:
                logging.error("截取游戏区域失败")
                return False
            
            # 计算网格
            cell_size = self.screen_capture.calculate_grid(game_image)
            if not cell_size:
                logging.error("计算网格失败")
                return False
            
            # 获取所有单元格图像
            grid_images = []
            for row in range(self.config['grid_rows']):
                row_images = []
                for col in range(self.config['grid_cols']):
                    cell_image = self.screen_capture.get_cell_image(row, col, game_image)
                    if cell_image:
                        # 预处理图像
                        processed_image = self.screen_capture.preprocess_image(cell_image)
                        row_images.append(processed_image)
                    else:
                        row_images.append(None)
                grid_images.append(row_images)
            
            # 学习图案
            self.image_recognition.learn_patterns_from_grid(grid_images)
            
            # 获取学习结果
            stats = self.image_recognition.get_pattern_statistics()
            if stats['total_patterns'] > 0:
                self.patterns_learned = True
                logging.info(f"图案学习完成，识别到 {stats['total_patterns']} 种图案")
                return True
            else:
                logging.warning("未能识别到任何图案")
                return False
                
        except Exception as e:
            logging.error(f"学习图案失败: {e}")
            return False

    def scan_current_grid(self) -> Optional[List[List[str]]]:
        """
        扫描当前游戏网格

        Returns:
            list: 二维网格，包含图案ID
        """
        try:
            if not self.game_region or not self.patterns_learned:
                return None

            # 截取游戏区域
            game_image = self.screen_capture.capture_screen(self.game_region)
            if not game_image:
                return None

            # 扫描网格
            grid = []
            for row in range(self.config['grid_rows']):
                row_patterns = []
                for col in range(self.config['grid_cols']):
                    cell_image = self.screen_capture.get_cell_image(row, col, game_image)
                    if cell_image:
                        processed_image = self.screen_capture.preprocess_image(cell_image)
                        pattern_id = self.image_recognition.identify_pattern(processed_image)
                        row_patterns.append(pattern_id if pattern_id else 'empty')
                    else:
                        row_patterns.append('empty')
                grid.append(row_patterns)

            self.current_grid = grid
            return grid

        except Exception as e:
            logging.error(f"扫描网格失败: {e}")
            return None

    def find_and_execute_match(self) -> bool:
        """
        查找并执行一次匹配

        Returns:
            bool: 是否成功执行匹配
        """
        try:
            start_time = time.time()

            # 扫描当前网格
            grid = self.scan_current_grid()
            if not grid:
                return False

            # 设置路径查找器的网格
            self.path_finder.set_grid(grid)

            # 查找最佳匹配
            match = self.path_finder.find_best_match()
            if not match:
                logging.info("未找到可连接的图案")
                return False

            pos1, pos2 = match
            logging.info(f"找到匹配: {pos1} <-> {pos2}")

            # 获取屏幕坐标
            screen_pos1 = self.screen_capture.get_grid_center(pos1[0], pos1[1])
            screen_pos2 = self.screen_capture.get_grid_center(pos2[0], pos2[1])

            if not screen_pos1 or not screen_pos2:
                logging.error("获取屏幕坐标失败")
                return False

            # 执行点击
            success = self.auto_clicker.click_pair(screen_pos1, screen_pos2)

            # 更新性能监控
            processing_time = time.time() - start_time
            update_performance_monitor(self.performance_monitor, success, processing_time)

            if success:
                logging.info(f"成功执行匹配: {screen_pos1} -> {screen_pos2}")
                return True
            else:
                logging.error("执行点击失败")
                return False

        except Exception as e:
            logging.error(f"查找并执行匹配失败: {e}")
            return False

    def run_auto_mode(self):
        """运行自动模式"""
        try:
            logging.info("开始自动模式")
            self.auto_clicker.start_auto_mode()

            consecutive_failures = 0
            max_failures = 5

            while self.is_running and self.auto_clicker.is_auto_running():
                # 尝试执行一次匹配
                success = self.find_and_execute_match()

                if success:
                    consecutive_failures = 0
                    # 成功后等待一段时间
                    time.sleep(self.config['click_delay'] * 2)
                else:
                    consecutive_failures += 1
                    if consecutive_failures >= max_failures:
                        logging.warning("连续失败次数过多，可能游戏已结束")
                        break

                    # 失败后等待更长时间
                    time.sleep(1.0)

                # 偶尔模拟人类暂停
                if self.config['enable_randomization'] and success:
                    import random
                    if random.random() < 0.1:  # 10%概率
                        self.auto_clicker.simulate_human_pause()

            logging.info("自动模式结束")

        except Exception as e:
            logging.error(f"自动模式运行失败: {e}")
        finally:
            self.stop()

    def start(self):
        """启动游戏辅助"""
        if self.is_running:
            return False

        # 检查前置条件
        if not self.game_region:
            logging.error("请先检测游戏区域")
            return False

        if not self.patterns_learned:
            logging.error("请先学习图案")
            return False

        self.is_running = True

        # 在新线程中运行自动模式
        threading.Thread(target=self.run_auto_mode, daemon=True).start()

        return True

    def stop(self):
        """停止游戏辅助"""
        self.is_running = False
        self.auto_clicker.stop_auto_mode()
        logging.info("游戏辅助已停止")

    def get_status(self) -> dict:
        """
        获取当前状态

        Returns:
            dict: 状态信息
        """
        stats = get_performance_stats(self.performance_monitor)

        return {
            'is_running': self.is_running,
            'game_region': format_region(self.game_region),
            'patterns_learned': self.patterns_learned,
            'pattern_count': len(self.image_recognition.pattern_templates),
            'performance': stats
        }


def main():
    """主函数"""
    try:
        print("连连看游戏辅助工具 v1.0")
        print("正在启动...")

        # 创建游戏辅助器
        assistant = GameAssistant()

        # 创建并运行GUI
        gui = MainWindow()

        # 将辅助器绑定到GUI
        gui.game_assistant = assistant

        # 运行GUI
        gui.run()

    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行错误: {e}")
        logging.error(f"程序运行错误: {e}")
    finally:
        print("程序已退出")


if __name__ == "__main__":
    main()
