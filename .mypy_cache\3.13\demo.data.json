{".class": "MypyFile", "_fullname": "demo", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AutoClicker": {".class": "SymbolTableNode", "cross_ref": "modules.auto_clicker.AutoClicker", "kind": "Gdef"}, "ImageRecognition": {".class": "SymbolTableNode", "cross_ref": "modules.image_recognition.ImageRecognition", "kind": "Gdef"}, "PathFinder": {".class": "SymbolTableNode", "cross_ref": "modules.path_finder.PathFinder", "kind": "Gdef"}, "ScreenCapture": {".class": "SymbolTableNode", "cross_ref": "modules.screen_capture.ScreenCapture", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "demo.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "demo.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "demo.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "demo.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "demo.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "demo.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "demo_auto_clicker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "demo.demo_auto_clicker", "name": "demo_auto_clicker", "type": null}}, "demo_config_system": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "demo.demo_config_system", "name": "demo_config_system", "type": null}}, "demo_image_recognition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "demo.demo_image_recognition", "name": "demo_image_recognition", "type": null}}, "demo_path_finder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "demo.demo_path_finder", "name": "demo_path_finder", "type": null}}, "demo_screen_capture": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "demo.demo_screen_capture", "name": "demo_screen_capture", "type": null}}, "get_screen_info": {".class": "SymbolTableNode", "cross_ref": "utils.helpers.get_screen_info", "kind": "Gdef"}, "load_config": {".class": "SymbolTableNode", "cross_ref": "utils.helpers.load_config", "kind": "Gdef"}, "main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "demo.main", "name": "main", "type": null}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}}, "path": "E:\\db\\0617\\连连看辅助工具\\demo.py"}