{"data_mtime": 1751812925, "dep_lines": [17, 24, 31, 38, 45, 91, 6, 7, 91, 92, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [20, 20, 20, 20, 20, 20, 10, 10, 20, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["modules.screen_capture", "modules.image_recognition", "modules.path_finder", "modules.auto_clicker", "utils.helpers", "PIL.Image", "sys", "os", "PIL", "numpy", "builtins", "_frozen_importlib", "_typeshed", "abc", "modules", "typing", "utils"], "hash": "e8b19b0e3bd46049ceafd6727e4124668329efdd", "id": "test_modules", "ignore_all": false, "interface_hash": "493bd2be53bd987ab1002003cbb954ed7ae26ee8", "mtime": 1751812942, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\db\\0617\\连连看辅助工具\\test_modules.py", "plugin_data": null, "size": 8004, "suppressed": [], "version_id": "1.15.0"}