{"data_mtime": 1751813500, "dep_lines": [7, 7, 7, 10, 10, 421, 6, 8, 9, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 20, 10, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["tkinter.ttk", "tkinter.messagebox", "tkinter.filedialog", "PIL.Image", "PIL.ImageTk", "utils.helpers", "tkinter", "threading", "time", "PIL", "builtins", "_collections_abc", "_frozen_importlib", "_tkinter", "_typeshed", "abc", "tkinter.font", "typing", "utils"], "hash": "2af660ded50c10fee8327dd8667beb17812e1e5f", "id": "gui.main_window", "ignore_all": false, "interface_hash": "3f6dd06948beb494ad08be6a026a8ec8fabf5cab", "mtime": 1751813518, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\db\\0617\\连连看辅助工具\\gui\\main_window.py", "plugin_data": null, "size": 19622, "suppressed": [], "version_id": "1.15.0"}