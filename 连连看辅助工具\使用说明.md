# 连连看游戏辅助工具使用说明

## 概述

这是一个专为连连看游戏设计的智能辅助工具，能够自动识别游戏界面上的图案并执行消除操作。工具采用计算机视觉技术和智能路径算法，可以快速找到可连接的图案对并自动完成点击操作。

## 功能特性

### 🎯 核心功能
- **自动图案识别**: 使用先进的图像识别算法，能够准确识别游戏中的各种图案
- **智能路径查找**: 实现连连看核心算法，支持直线连接和一个拐角连接
- **自动化操作**: 模拟人类操作，自动执行鼠标点击
- **人性化行为**: 支持随机延迟和位置偏移，避免被检测

### 🔧 高级特性
- **自适应游戏区域检测**: 自动识别游戏界面位置
- **可配置参数**: 支持自定义网格大小、点击延迟等参数
- **实时监控**: 提供运行状态和性能统计
- **调试模式**: 支持保存调试图像，便于问题排查

## 安装和配置

### 系统要求
- Windows 10/11
- Python 3.8 或更高版本
- 1920x1080 分辨率（推荐）

### 安装步骤

1. **安装Python依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **运行测试**
   ```bash
   python test_modules.py
   ```

3. **启动程序**
   ```bash
   python main.py
   ```

## 使用方法

### 第一次使用

1. **启动程序**
   - 运行 `main.py` 启动图形界面
   - 界面会显示当前状态和控制选项

2. **打开连连看游戏**
   - 确保游戏窗口完全可见
   - 游戏界面不要被其他窗口遮挡

3. **检测游戏区域**
   - 点击"检测游戏区域"按钮
   - 程序会自动识别游戏界面位置
   - 如果自动检测失败，可以手动设置区域

4. **学习图案**
   - 点击"学习图案"按钮
   - 程序会分析当前游戏界面上的所有图案
   - 学习完成后会显示识别到的图案数量

5. **开始辅助**
   - 点击"开始辅助"按钮
   - 程序开始自动寻找并消除可连接的图案

### 参数设置

#### 基本参数
- **点击延迟**: 两次点击之间的间隔时间（0.1-2.0秒）
- **网格大小**: 游戏网格的行列数（根据具体游戏调整）
- **人性化操作**: 启用随机延迟和位置偏移

#### 高级参数（config.json）
```json
{
    "click_delay": 0.3,
    "move_duration": 0.1,
    "grid_rows": 12,
    "grid_cols": 8,
    "similarity_threshold": 0.8,
    "enable_randomization": true,
    "position_variance": 3,
    "timing_variance": 0.05
}
```

## 操作技巧

### 最佳实践

1. **游戏准备**
   - 确保游戏界面清晰可见
   - 关闭其他可能干扰的程序
   - 建议使用1920x1080分辨率

2. **首次设置**
   - 先在简单关卡测试工具效果
   - 根据游戏特点调整网格大小
   - 适当调整点击延迟避免过快操作

3. **运行监控**
   - 观察日志输出了解运行状态
   - 注意成功率和错误信息
   - 必要时重新学习图案

### 常见问题解决

#### 识别不准确
- 重新学习图案
- 调整相似度阈值
- 检查游戏界面是否清晰

#### 点击位置偏移
- 重新检测游戏区域
- 调整网格大小参数
- 确认游戏窗口没有移动

#### 运行速度过快
- 增加点击延迟
- 启用人性化操作
- 适当增加随机暂停

## 安全提示

### 使用注意事项

1. **合规使用**
   - 仅在允许使用辅助工具的游戏中使用
   - 遵守游戏的使用条款和相关法律法规
   - 不要在竞技或排名模式中使用

2. **安全设置**
   - 启用人性化操作避免被检测
   - 适当设置操作间隔
   - 避免长时间连续使用

3. **紧急停止**
   - 将鼠标移动到屏幕左上角可紧急停止
   - 使用"停止辅助"按钮正常停止
   - 关闭程序窗口也会停止所有操作

## 故障排除

### 常见错误

#### 模块导入失败
```
解决方案：
1. 检查Python版本是否符合要求
2. 重新安装依赖包：pip install -r requirements.txt
3. 确认所有文件完整
```

#### 截图失败
```
解决方案：
1. 检查屏幕权限设置
2. 关闭可能冲突的屏幕录制软件
3. 重启程序
```

#### 游戏区域检测失败
```
解决方案：
1. 手动设置游戏区域坐标
2. 调整游戏窗口大小和位置
3. 确保游戏界面对比度足够
```

### 调试模式

启用调试模式可以保存中间处理图像：

```python
# 在config.json中设置
"save_debug_images": true
```

调试图像会保存在 `debug_images` 目录中，包括：
- 全屏截图
- 游戏区域截图
- 单元格图像
- 处理后的图像

## 技术支持

### 日志文件
程序运行时会生成日志文件 `game_assistant.log`，包含详细的运行信息和错误记录。

### 性能监控
程序提供实时性能统计：
- 运行时间
- 操作次数
- 成功率
- 平均处理时间

### 联系方式
如遇到问题或需要技术支持，请：
1. 查看日志文件中的错误信息
2. 运行测试脚本检查模块状态
3. 提供详细的错误描述和系统信息

## 免责声明

本工具仅供学习和研究使用，使用者需要：
- 遵守相关法律法规
- 遵守游戏使用条款
- 承担使用风险
- 不得用于商业用途

开发者不对使用本工具造成的任何后果承担责任。
