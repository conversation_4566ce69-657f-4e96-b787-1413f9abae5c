"""
连连看辅助工具演示脚本
展示程序的各项功能
"""

import sys
import os
import time

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.screen_capture import ScreenCapture
from modules.image_recognition import ImageRecognition
from modules.path_finder import PathFinder
from modules.auto_clicker import AutoClicker
from utils.helpers import load_config, get_screen_info

def demo_screen_capture():
    """演示屏幕截图功能"""
    print("\n" + "="*50)
    print("📸 屏幕截图功能演示")
    print("="*50)
    
    capture = ScreenCapture()
    
    # 全屏截图
    print("正在进行全屏截图...")
    screenshot = capture.capture_screen()
    if screenshot:
        print(f"✓ 全屏截图成功，尺寸: {screenshot.size}")
        
        # 保存截图
        screenshot.save("demo_screenshot.png")
        print("✓ 截图已保存为 demo_screenshot.png")
    else:
        print("✗ 截图失败")
        return False
    
    # 网格计算演示
    print("\n正在演示网格计算...")
    capture.grid_size = (8, 12)
    cell_size = capture.calculate_grid(screenshot)
    if cell_size:
        print(f"✓ 网格计算成功，单元格大小: {cell_size}")
    else:
        print("✗ 网格计算失败")
    
    return True

def demo_image_recognition():
    """演示图像识别功能"""
    print("\n" + "="*50)
    print("🔍 图像识别功能演示")
    print("="*50)
    
    from PIL import Image
    import numpy as np
    
    recognizer = ImageRecognition()
    
    # 创建测试图像
    print("创建测试图像...")
    
    # 红色图像
    red_image = Image.new('RGB', (64, 64), color='red')
    # 蓝色图像
    blue_image = Image.new('RGB', (64, 64), color='blue')
    # 空白图像
    empty_image = Image.new('RGB', (64, 64), color='white')
    
    print("✓ 测试图像创建完成")
    
    # 特征提取演示
    print("\n正在提取图像特征...")
    red_features = recognizer.extract_features(red_image)
    blue_features = recognizer.extract_features(blue_image)
    
    if red_features and blue_features:
        print("✓ 特征提取成功")
        print(f"  红色图像主要颜色: {red_features['dominant_color']}")
        print(f"  蓝色图像主要颜色: {blue_features['dominant_color']}")
    else:
        print("✗ 特征提取失败")
        return False
    
    # 相似度比较演示
    print("\n正在进行相似度比较...")
    same_similarity = recognizer.compare_images(red_image, red_image)
    diff_similarity = recognizer.compare_images(red_image, blue_image)
    
    print(f"✓ 相同图像相似度: {same_similarity:.2f}")
    print(f"✓ 不同图像相似度: {diff_similarity:.2f}")
    
    # 空单元格检测演示
    print("\n正在检测空单元格...")
    is_empty = recognizer.is_empty_cell(empty_image)
    is_not_empty = recognizer.is_empty_cell(red_image)
    
    print(f"✓ 空白图像检测结果: {is_empty}")
    print(f"✓ 彩色图像检测结果: {is_not_empty}")
    
    return True

def demo_path_finder():
    """演示路径查找功能"""
    print("\n" + "="*50)
    print("🛤️ 路径查找功能演示")
    print("="*50)
    
    # 创建测试网格
    test_grid = [
        ['A', 'B', 'A', 'empty'],
        ['empty', 'C', 'B', 'C'],
        ['D', 'empty', 'empty', 'D'],
        ['empty', 'empty', 'empty', 'empty']
    ]
    
    print("测试网格:")
    for i, row in enumerate(test_grid):
        print(f"  行 {i}: {row}")
    
    finder = PathFinder()
    finder.set_grid(test_grid)
    
    # 连接测试
    print("\n正在测试连接检查...")
    
    test_cases = [
        ((0, 0), (0, 2), "A-A 直线连接"),
        ((1, 1), (1, 3), "C-C 直线连接"),
        ((2, 0), (2, 3), "D-D 拐角连接"),
        ((0, 1), (1, 2), "B-B 拐角连接"),
        ((0, 0), (1, 1), "A-C 不同图案")
    ]
    
    for pos1, pos2, description in test_cases:
        result = finder.can_connect(pos1, pos2)
        status = "✓" if result['can_connect'] else "✗"
        print(f"  {status} {description}: {result['path_type']}")
        
        if result['can_connect']:
            path = finder.get_path_points(pos1, pos2)
            print(f"    路径: {path}")
    
    # 查找所有匹配
    print("\n正在查找所有可连接的匹配...")
    matches = finder.find_all_matches()
    print(f"✓ 找到 {len(matches)} 个可连接的匹配:")
    for i, match in enumerate(matches, 1):
        print(f"  {i}. {match[0]} <-> {match[1]}")
    
    # 网格统计
    print("\n网格统计信息:")
    stats = finder.get_grid_statistics()
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    return True

def demo_auto_clicker():
    """演示自动点击功能"""
    print("\n" + "="*50)
    print("🖱️ 自动点击功能演示")
    print("="*50)
    
    clicker = AutoClicker()
    
    # 配置演示
    print("配置自动点击器...")
    clicker.set_click_delay(0.1)
    clicker.set_move_duration(0.1)
    clicker.enable_human_like_behavior(False)
    print("✓ 配置完成")
    
    # 获取当前鼠标位置
    current_pos = clicker.get_mouse_position()
    print(f"✓ 当前鼠标位置: {current_pos}")
    
    # 坐标验证演示
    print("\n正在验证坐标...")
    valid_coords = [(100, 100), (1920, 1080), (-10, 50), (2000, 2000)]
    
    for x, y in valid_coords:
        is_valid = clicker.validate_click_area(x, y)
        status = "✓" if is_valid else "✗"
        print(f"  {status} 坐标 ({x}, {y}): {'有效' if is_valid else '无效'}")
    
    # 统计信息
    print("\n点击器统计信息:")
    stats = clicker.get_click_statistics()
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    print("\n⚠️ 注意: 实际点击功能已禁用以避免意外操作")
    
    return True

def demo_config_system():
    """演示配置系统"""
    print("\n" + "="*50)
    print("⚙️ 配置系统演示")
    print("="*50)
    
    # 加载配置
    print("正在加载配置...")
    config = load_config()
    print(f"✓ 配置加载成功，共 {len(config)} 个配置项:")
    
    for key, value in config.items():
        print(f"  {key}: {value}")
    
    # 屏幕信息
    print("\n正在获取屏幕信息...")
    screen_info = get_screen_info()
    print("✓ 屏幕信息:")
    for key, value in screen_info.items():
        print(f"  {key}: {value}")
    
    return True

def main():
    """主演示函数"""
    print("🎮 连连看辅助工具功能演示")
    print("本演示将展示程序的各项核心功能")
    
    demos = [
        ("屏幕截图", demo_screen_capture),
        ("图像识别", demo_image_recognition),
        ("路径查找", demo_path_finder),
        ("自动点击", demo_auto_clicker),
        ("配置系统", demo_config_system)
    ]
    
    success_count = 0
    
    for demo_name, demo_func in demos:
        try:
            print(f"\n{'='*60}")
            print(f"开始演示: {demo_name}")
            
            if demo_func():
                success_count += 1
                print(f"✅ {demo_name} 演示完成")
            else:
                print(f"❌ {demo_name} 演示失败")
                
        except Exception as e:
            print(f"❌ {demo_name} 演示出错: {e}")
        
        # 短暂暂停
        time.sleep(1)
    
    print(f"\n{'='*60}")
    print(f"🎉 演示完成！")
    print(f"成功演示: {success_count}/{len(demos)} 个功能")
    
    if success_count == len(demos):
        print("🎊 所有功能演示成功！连连看辅助工具运行正常。")
    else:
        print("⚠️ 部分功能演示失败，请检查相关模块。")
    
    print("\n💡 提示: 运行 run_assistant.py 启动完整的图形界面程序")

if __name__ == "__main__":
    main()
