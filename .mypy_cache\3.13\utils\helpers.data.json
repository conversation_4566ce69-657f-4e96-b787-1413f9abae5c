{".class": "MypyFile", "_fullname": "utils.helpers", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.helpers.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.helpers.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.helpers.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.helpers.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.helpers.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.helpers.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "calculate_distance": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["pos1", "pos2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.helpers.calculate_distance", "name": "calculate_distance", "type": null}}, "calculate_grid_metrics": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["region", "grid_rows", "grid_cols"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.helpers.calculate_grid_metrics", "name": "calculate_grid_metrics", "type": null}}, "clamp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["value", "min_value", "max_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.helpers.clamp", "name": "clamp", "type": null}}, "cleanup_old_files": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["directory", "max_age_days"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.helpers.cleanup_old_files", "name": "cleanup_old_files", "type": null}}, "config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "utils.helpers.config", "name": "config", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "create_debug_directory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.helpers.create_debug_directory", "name": "create_debug_directory", "type": null}}, "create_performance_monitor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.helpers.create_performance_monitor", "name": "create_performance_monitor", "type": null}}, "format_region": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["region"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.helpers.format_region", "name": "format_region", "type": null}}, "format_time": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["seconds"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.helpers.format_time", "name": "format_time", "type": null}}, "generate_timestamp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.helpers.generate_timestamp", "name": "generate_timestamp", "type": null}}, "get_performance_stats": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["monitor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.helpers.get_performance_stats", "name": "get_performance_stats", "type": null}}, "get_screen_info": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.helpers.get_screen_info", "name": "get_screen_info", "type": null}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "load_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["config_file"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.helpers.load_config", "name": "load_config", "type": null}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "monitor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "utils.helpers.monitor", "name": "monitor", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "parse_region_string": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["region_str"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.helpers.parse_region_string", "name": "parse_region_string", "type": null}}, "safe_divide": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.helpers.safe_divide", "name": "safe_divide", "type": null}}, "save_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["config", "config_file"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.helpers.save_config", "name": "save_config", "type": null}}, "screen_info": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "utils.helpers.screen_info", "name": "screen_info", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup_logging": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1], "arg_names": ["log_file", "level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.helpers.setup_logging", "name": "setup_logging", "type": null}}, "stats": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "utils.helpers.stats", "name": "stats", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "update_performance_monitor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["monitor", "success", "processing_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.helpers.update_performance_monitor", "name": "update_performance_monitor", "type": null}}, "validate_grid_position": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["row", "col", "grid_rows", "grid_cols"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.helpers.validate_grid_position", "name": "validate_grid_position", "type": null}}}, "path": "E:\\db\\0617\\连连看辅助工具\\utils\\helpers.py"}