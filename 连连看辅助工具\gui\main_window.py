"""
主窗口GUI界面
提供用户友好的图形界面来控制连连看辅助工具
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
from PIL import Image, ImageTk


class MainWindow:
    """主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        self.root = tk.Tk()
        self.root.title("连连看游戏辅助工具 v1.0")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        # 设置窗口图标和样式
        self.setup_styles()
        
        # 状态变量
        self.is_running = False
        self.game_assistant = None
        
        # 创建界面组件
        self.create_widgets()
        
        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_styles(self):
        """设置界面样式"""
        # 配置ttk样式
        style = ttk.Style()
        style.theme_use('clam')
        
        # 自定义样式
        style.configure('Title.TLabel', font=('微软雅黑', 16, 'bold'))
        style.configure('Heading.TLabel', font=('微软雅黑', 12, 'bold'))
        style.configure('Status.TLabel', font=('微软雅黑', 10))
        style.configure('Big.TButton', font=('微软雅黑', 12, 'bold'))
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="连连看游戏辅助工具", style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 状态显示区域
        self.create_status_section(main_frame, 1)
        
        # 控制按钮区域
        self.create_control_section(main_frame, 2)
        
        # 设置区域
        self.create_settings_section(main_frame, 3)
        
        # 日志显示区域
        self.create_log_section(main_frame, 4)
        
        # 底部信息
        self.create_footer(main_frame, 5)
    
    def create_status_section(self, parent, row):
        """创建状态显示区域"""
        # 状态框架
        status_frame = ttk.LabelFrame(parent, text="运行状态", padding="10")
        status_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        status_frame.columnconfigure(1, weight=1)
        
        # 运行状态
        ttk.Label(status_frame, text="状态:", style='Heading.TLabel').grid(row=0, column=0, sticky=tk.W)
        self.status_label = ttk.Label(status_frame, text="未启动", style='Status.TLabel', foreground='red')
        self.status_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # 游戏区域状态
        ttk.Label(status_frame, text="游戏区域:", style='Heading.TLabel').grid(row=1, column=0, sticky=tk.W)
        self.region_label = ttk.Label(status_frame, text="未检测", style='Status.TLabel')
        self.region_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        # 识别状态
        ttk.Label(status_frame, text="图案识别:", style='Heading.TLabel').grid(row=2, column=0, sticky=tk.W)
        self.recognition_label = ttk.Label(status_frame, text="未初始化", style='Status.TLabel')
        self.recognition_label.grid(row=2, column=1, sticky=tk.W, padx=(10, 0))

        # 运行统计
        ttk.Label(status_frame, text="运行统计:", style='Heading.TLabel').grid(row=3, column=0, sticky=tk.W)
        self.stats_label = ttk.Label(status_frame, text="未开始", style='Status.TLabel')
        self.stats_label.grid(row=3, column=1, sticky=tk.W, padx=(10, 0))
    
    def create_control_section(self, parent, row):
        """创建控制按钮区域"""
        # 控制框架
        control_frame = ttk.LabelFrame(parent, text="控制面板", padding="10")
        control_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 按钮框架
        button_frame = ttk.Frame(control_frame)
        button_frame.grid(row=0, column=0, columnspan=3, pady=(0, 10))
        
        # 主要控制按钮
        self.start_button = ttk.Button(button_frame, text="开始辅助", style='Big.TButton', 
                                      command=self.start_assistant, width=12)
        self.start_button.grid(row=0, column=0, padx=(0, 10))
        
        self.stop_button = ttk.Button(button_frame, text="停止辅助", style='Big.TButton', 
                                     command=self.stop_assistant, width=12, state='disabled')
        self.stop_button.grid(row=0, column=1, padx=(0, 10))
        
        self.detect_button = ttk.Button(button_frame, text="检测游戏区域", 
                                       command=self.detect_game_region, width=15)
        self.detect_button.grid(row=0, column=2)
        
        # 辅助功能按钮
        aux_frame = ttk.Frame(control_frame)
        aux_frame.grid(row=1, column=0, columnspan=3, pady=(10, 0))
        
        ttk.Button(aux_frame, text="截图测试", command=self.test_screenshot, width=12).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(aux_frame, text="学习图案", command=self.learn_patterns, width=12).grid(row=0, column=1, padx=(0, 10))
        ttk.Button(aux_frame, text="保存设置", command=self.save_settings, width=12).grid(row=0, column=2)
    
    def create_settings_section(self, parent, row):
        """创建设置区域"""
        # 设置框架
        settings_frame = ttk.LabelFrame(parent, text="参数设置", padding="10")
        settings_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        settings_frame.columnconfigure(1, weight=1)
        
        # 点击延迟设置
        ttk.Label(settings_frame, text="点击延迟(秒):").grid(row=0, column=0, sticky=tk.W)
        self.delay_var = tk.DoubleVar(value=0.3)
        delay_scale = ttk.Scale(settings_frame, from_=0.1, to=2.0, variable=self.delay_var, 
                               orient=tk.HORIZONTAL, length=200)
        delay_scale.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0))
        self.delay_label = ttk.Label(settings_frame, text="0.3")
        self.delay_label.grid(row=0, column=2, padx=(10, 0))
        delay_scale.configure(command=self.update_delay_label)
        
        # 网格大小设置
        ttk.Label(settings_frame, text="网格大小:").grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        grid_frame = ttk.Frame(settings_frame)
        grid_frame.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))
        
        self.cols_var = tk.IntVar(value=8)
        self.rows_var = tk.IntVar(value=12)
        
        ttk.Label(grid_frame, text="列:").grid(row=0, column=0)
        ttk.Spinbox(grid_frame, from_=4, to=20, width=5, textvariable=self.cols_var).grid(row=0, column=1, padx=(5, 10))
        ttk.Label(grid_frame, text="行:").grid(row=0, column=2)
        ttk.Spinbox(grid_frame, from_=4, to=20, width=5, textvariable=self.rows_var).grid(row=0, column=3, padx=(5, 0))
        
        # 人性化操作
        self.humanize_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(settings_frame, text="启用人性化操作（随机延迟和位置偏移）", 
                       variable=self.humanize_var).grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=(10, 0))
    
    def create_log_section(self, parent, row):
        """创建日志显示区域"""
        # 日志框架
        log_frame = ttk.LabelFrame(parent, text="运行日志", padding="10")
        log_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 日志文本框
        self.log_text = tk.Text(log_frame, height=8, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 滚动条
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        # 配置主框架的行权重
        parent.rowconfigure(row, weight=1)
    
    def create_footer(self, parent, row):
        """创建底部信息"""
        footer_frame = ttk.Frame(parent)
        footer_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        footer_frame.columnconfigure(1, weight=1)
        
        ttk.Label(footer_frame, text="提示: 将鼠标移到屏幕左上角可紧急停止", 
                 foreground='blue').grid(row=0, column=0, sticky=tk.W)
        
        ttk.Label(footer_frame, text="© 2025 连连看辅助工具", 
                 foreground='gray').grid(row=0, column=1, sticky=tk.E)
    
    def update_delay_label(self, value):
        """更新延迟标签"""
        self.delay_label.config(text=f"{float(value):.1f}")
    
    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # 限制日志长度
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 100:
            self.log_text.delete("1.0", "10.0")
    
    def update_status(self, status, color='black'):
        """更新状态显示"""
        self.status_label.config(text=status, foreground=color)
    
    def update_region_status(self, region_info):
        """更新游戏区域状态"""
        self.region_label.config(text=region_info)
    
    def update_recognition_status(self, recognition_info):
        """更新识别状态"""
        self.recognition_label.config(text=recognition_info)

    def update_stats_status(self, stats_info):
        """更新统计状态"""
        self.stats_label.config(text=stats_info)
    
    def start_assistant(self):
        """启动辅助功能"""
        if self.is_running:
            return
        
        try:
            self.is_running = True
            self.start_button.config(state='disabled')
            self.stop_button.config(state='normal')
            self.update_status("正在启动...", 'orange')
            
            self.log_message("开始启动连连看辅助工具")
            
            # 在新线程中运行辅助功能
            threading.Thread(target=self._run_assistant, daemon=True).start()
            
        except Exception as e:
            self.log_message(f"启动失败: {e}")
            self.stop_assistant()
    
    def stop_assistant(self):
        """停止辅助功能"""
        self.is_running = False
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.update_status("已停止", 'red')
        
        if self.game_assistant:
            self.game_assistant.stop()
        
        self.log_message("连连看辅助工具已停止")
    
    def _run_assistant(self):
        """运行辅助功能的主循环"""
        try:
            if not self.game_assistant:
                self.log_message("错误: 游戏辅助器未初始化")
                self.stop_assistant()
                return

            self.update_status("运行中", 'green')
            self.log_message("辅助工具开始运行...")

            # 启动游戏辅助器
            success = self.game_assistant.start()
            if not success:
                self.log_message("启动失败，请检查前置条件")
                self.stop_assistant()
                return

            # 监控运行状态
            while self.is_running:
                time.sleep(2)

                # 获取状态信息
                status = self.game_assistant.get_status()
                perf = status['performance']

                # 更新统计显示
                stats_text = f"操作{perf['total_operations']}次, 成功率{perf['success_rate']:.1%}"
                self.root.after(0, lambda: self.update_stats_status(stats_text))

                # 更新日志
                if perf['total_operations'] > 0:
                    self.root.after(0, lambda: self.log_message(
                        f"运行中: {stats_text}, 平均耗时{perf['average_processing_time']:.2f}秒"))

                # 检查是否还在运行
                if not self.game_assistant.is_running:
                    self.root.after(0, lambda: self.log_message("辅助工具自动停止"))
                    break

        except Exception as e:
            self.log_message(f"运行错误: {e}")
        finally:
            if self.is_running:
                self.stop_assistant()
    
    def detect_game_region(self):
        """检测游戏区域"""
        if not self.game_assistant:
            self.log_message("错误: 游戏辅助器未初始化")
            return

        self.log_message("开始检测游戏区域...")
        self.update_region_status("检测中...")
        self.detect_button.config(state='disabled')

        def detect_in_thread():
            try:
                region = self.game_assistant.detect_game_region()
                if region:
                    region_text = f"已检测 ({region[0]}, {region[1]}) {region[2]}x{region[3]}"
                    self.root.after(0, lambda: self.update_region_status(region_text))
                    self.root.after(0, lambda: self.log_message(f"游戏区域检测完成: {region_text}"))
                else:
                    self.root.after(0, lambda: self.update_region_status("检测失败"))
                    self.root.after(0, lambda: self.log_message("游戏区域检测失败，请手动调整游戏窗口"))
            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"检测错误: {e}"))
                self.root.after(0, lambda: self.update_region_status("检测错误"))
            finally:
                self.root.after(0, lambda: self.detect_button.config(state='normal'))

        threading.Thread(target=detect_in_thread, daemon=True).start()
    
    def test_screenshot(self):
        """测试截图功能"""
        if not self.game_assistant:
            self.log_message("错误: 游戏辅助器未初始化")
            return

        self.log_message("执行截图测试...")

        def screenshot_in_thread():
            try:
                # 全屏截图
                screenshot = self.game_assistant.screen_capture.capture_screen()
                if screenshot:
                    self.root.after(0, lambda: self.log_message(f"全屏截图成功，尺寸: {screenshot.size}"))

                    # 如果有游戏区域，也截取游戏区域
                    if self.game_assistant.game_region:
                        game_screenshot = self.game_assistant.screen_capture.capture_screen(
                            self.game_assistant.game_region)
                        if game_screenshot:
                            self.root.after(0, lambda: self.log_message(
                                f"游戏区域截图成功，尺寸: {game_screenshot.size}"))
                        else:
                            self.root.after(0, lambda: self.log_message("游戏区域截图失败"))
                    else:
                        self.root.after(0, lambda: self.log_message("提示: 可先检测游戏区域"))
                else:
                    self.root.after(0, lambda: self.log_message("截图失败"))
            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"截图测试错误: {e}"))

        threading.Thread(target=screenshot_in_thread, daemon=True).start()
    
    def learn_patterns(self):
        """学习图案"""
        if not self.game_assistant:
            self.log_message("错误: 游戏辅助器未初始化")
            return

        if not self.game_assistant.game_region:
            self.log_message("错误: 请先检测游戏区域")
            return

        self.log_message("开始学习图案...")
        self.update_recognition_status("学习中...")

        def learn_in_thread():
            try:
                success = self.game_assistant.learn_patterns()
                if success:
                    pattern_count = len(self.game_assistant.image_recognition.pattern_templates)
                    status_text = f"已识别 {pattern_count} 种图案"
                    self.root.after(0, lambda: self.update_recognition_status(status_text))
                    self.root.after(0, lambda: self.log_message(f"图案学习完成: {status_text}"))
                else:
                    self.root.after(0, lambda: self.update_recognition_status("学习失败"))
                    self.root.after(0, lambda: self.log_message("图案学习失败，请检查游戏界面"))
            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"学习错误: {e}"))
                self.root.after(0, lambda: self.update_recognition_status("学习错误"))

        threading.Thread(target=learn_in_thread, daemon=True).start()
    
    def save_settings(self):
        """保存设置"""
        try:
            settings = {
                'click_delay': self.delay_var.get(),
                'grid_cols': self.cols_var.get(),
                'grid_rows': self.rows_var.get(),
                'enable_randomization': self.humanize_var.get()
            }

            # 应用设置到游戏辅助器
            if self.game_assistant:
                self.game_assistant.config.update(settings)
                self.game_assistant.configure_modules()

                # 保存到配置文件
                from utils.helpers import save_config
                save_config(self.game_assistant.config)

                self.log_message(f"设置已保存并应用: 延迟{settings['click_delay']}秒, "
                               f"网格{settings['grid_cols']}x{settings['grid_rows']}, "
                               f"人性化{'开启' if settings['enable_randomization'] else '关闭'}")
                messagebox.showinfo("设置", "设置已保存并应用到辅助工具")
            else:
                self.log_message(f"设置已保存: {settings}")
                messagebox.showinfo("设置", "设置已保存")

        except Exception as e:
            self.log_message(f"保存设置失败: {e}")
            messagebox.showerror("错误", f"保存设置失败: {e}")
    
    def on_closing(self):
        """窗口关闭事件"""
        if self.is_running:
            if messagebox.askokcancel("退出", "辅助工具正在运行，确定要退出吗？"):
                self.stop_assistant()
                self.root.destroy()
        else:
            self.root.destroy()
    
    def run(self):
        """运行主窗口"""
        self.log_message("连连看辅助工具已启动")
        self.log_message("请先检测游戏区域，然后学习图案，最后开始辅助")
        self.root.mainloop()


# 测试代码
if __name__ == "__main__":
    app = MainWindow()
    app.run()
