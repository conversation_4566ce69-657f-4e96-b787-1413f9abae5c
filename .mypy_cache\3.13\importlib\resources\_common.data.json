{".class": "MypyFile", "_fullname": "importlib.resources._common", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AbstractContextManager": {".class": "SymbolTableNode", "cross_ref": "contextlib.AbstractContextManager", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Anchor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 8, "fullname": "importlib.resources._common.Anchor", "line": 16, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeAliasType", "args": [], "type_ref": "importlib.resources._common.Package"}}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Package": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "importlib.resources._common.Package", "line": 13, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", "types.ModuleType"], "uses_pep604_syntax": true}}}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ResourceReader": {".class": "SymbolTableNode", "cross_ref": "importlib.abc.ResourceReader", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Traversable": {".class": "SymbolTableNode", "cross_ref": "importlib.abc.Traversable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.resources._common.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.resources._common.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.resources._common.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.resources._common.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.resources._common.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.resources._common.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "as_file": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "importlib.resources._common.as_file", "name": "as_file", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["importlib.abc.Traversable"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_file", "ret_type": {".class": "Instance", "args": ["pathlib.Path", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "contextlib.AbstractContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deprecated": {".class": "SymbolTableNode", "cross_ref": "warnings.deprecated", "kind": "Gdef", "module_hidden": true, "module_public": false}, "files": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "importlib.resources._common.files", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["anchor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "importlib.resources._common.files", "name": "files", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["anchor"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "importlib.resources._common.Anchor"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "files", "ret_type": "importlib.abc.Traversable", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "importlib.resources._common.files", "name": "files", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["anchor"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "importlib.resources._common.Anchor"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "files", "ret_type": "importlib.abc.Traversable", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["package"], "dataclass_transform_spec": null, "deprecated": "overload def (package: Union[Union[builtins.str, types.ModuleType], None] =) -> importlib.abc.Traversable of function importlib.resources._common.files is deprecated: First parameter to files is renamed to 'anchor'", "flags": ["is_overload", "is_decorated"], "fullname": "importlib.resources._common.files", "name": "files", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["package"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "importlib.resources._common.Anchor"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "files", "ret_type": "importlib.abc.Traversable", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "importlib.resources._common.files", "name": "files", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["package"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "importlib.resources._common.Anchor"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "files", "ret_type": "importlib.abc.Traversable", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [1], "arg_names": ["anchor"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "importlib.resources._common.Anchor"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "files", "ret_type": "importlib.abc.Traversable", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [1], "arg_names": ["package"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "importlib.resources._common.Anchor"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "files", "ret_type": "importlib.abc.Traversable", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "from_package": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["package"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "importlib.resources._common.from_package", "name": "from_package", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["package"], "arg_types": ["types.ModuleType"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_package", "ret_type": "importlib.abc.Traversable", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_resource_reader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["package"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "importlib.resources._common.get_resource_reader", "name": "get_resource_reader", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["package"], "arg_types": ["types.ModuleType"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_resource_reader", "ret_type": {".class": "UnionType", "items": ["importlib.abc.ResourceReader", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "package_to_anchor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "importlib.resources._common.package_to_anchor", "name": "package_to_anchor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["func"], "arg_types": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "importlib.resources._common.Anchor"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "importlib.abc.Traversable", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "package_to_anchor", "ret_type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "importlib.resources._common.Anchor"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "importlib.resources._common.Anchor"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "importlib.abc.Traversable", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resolve": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cand"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "importlib.resources._common.resolve", "name": "resolve", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cand"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "importlib.resources._common.Anchor"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve", "ret_type": "types.ModuleType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\importlib\\resources\\_common.pyi"}