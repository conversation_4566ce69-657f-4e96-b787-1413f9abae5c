{".class": "MypyFile", "_fullname": "tkinter.messagebox", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ABORT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "final_value": "abort", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.messagebox.ABORT", "name": "ABORT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "abort"}, "type_ref": "builtins.str"}}}, "ABORTRETRYIGNORE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "final_value": "abortre<PERSON><PERSON><PERSON>", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.messagebox.ABORTRETRYIGNORE", "name": "ABORTRETRYIGNORE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "abortre<PERSON><PERSON><PERSON>"}, "type_ref": "builtins.str"}}}, "CANCEL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "final_value": "cancel", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.messagebox.CANCEL", "name": "CANCEL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "cancel"}, "type_ref": "builtins.str"}}}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Dialog": {".class": "SymbolTableNode", "cross_ref": "tkinter.commondialog.Dialog", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "final_value": "error", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.messagebox.ERROR", "name": "ERROR", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "error"}, "type_ref": "builtins.str"}}}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "IGNORE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "final_value": "ignore", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.messagebox.IGNORE", "name": "IGNORE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "ignore"}, "type_ref": "builtins.str"}}}, "INFO": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "final_value": "info", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.messagebox.INFO", "name": "INFO", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "info"}, "type_ref": "builtins.str"}}}, "Message": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tkinter.commondialog.Dialog"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tkinter.messagebox.Message", "name": "Message", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tkinter.messagebox.Message", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tkinter.messagebox", "mro": ["tkinter.messagebox.Message", "tkinter.commondialog.Dialog", "builtins.object"], "names": {".class": "SymbolTable", "command": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "tkinter.messagebox.Message.command", "name": "command", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tkinter.messagebox.Message.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tkinter.messagebox.Message", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NO": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "final_value": "no", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.messagebox.NO", "name": "NO", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "no"}, "type_ref": "builtins.str"}}}, "OK": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "final_value": "ok", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.messagebox.OK", "name": "OK", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "ok"}, "type_ref": "builtins.str"}}}, "OKCANCEL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "final_value": "okcancel", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.messagebox.OKCANCEL", "name": "OKCANCEL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "okcancel"}, "type_ref": "builtins.str"}}}, "QUESTION": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "final_value": "question", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.messagebox.QUESTION", "name": "QUESTION", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "question"}, "type_ref": "builtins.str"}}}, "RETRY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "final_value": "retry", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.messagebox.RETRY", "name": "RETRY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "retry"}, "type_ref": "builtins.str"}}}, "RETRYCANCEL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "final_value": "retrycancel", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.messagebox.RETRYCANCEL", "name": "RETRYCANCEL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "retrycancel"}, "type_ref": "builtins.str"}}}, "WARNING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "final_value": "warning", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.messagebox.WARNING", "name": "WARNING", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "warning"}, "type_ref": "builtins.str"}}}, "YES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "final_value": "yes", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.messagebox.YES", "name": "YES", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "yes"}, "type_ref": "builtins.str"}}}, "YESNO": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "final_value": "yesno", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.messagebox.YESNO", "name": "YESNO", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "yesno"}, "type_ref": "builtins.str"}}}, "YESNOCANCEL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "final_value": "yesnocancel", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.messagebox.YESNOCANCEL", "name": "YESNOCANCEL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "yesnocancel"}, "type_ref": "builtins.str"}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "tkinter.messagebox.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tkinter.messagebox.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tkinter.messagebox.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tkinter.messagebox.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tkinter.messagebox.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tkinter.messagebox.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tkinter.messagebox.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "askokcancel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 4], "arg_names": ["title", "message", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tkinter.messagebox.askokcancel", "name": "askokcancel", "type": {".class": "CallableType", "arg_kinds": [1, 1, 4], "arg_names": ["title", "message", "options"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "askokcancel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "askquestion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 4], "arg_names": ["title", "message", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tkinter.messagebox.askquestion", "name": "askquestion", "type": {".class": "CallableType", "arg_kinds": [1, 1, 4], "arg_names": ["title", "message", "options"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "askquestion", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "askretrycancel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 4], "arg_names": ["title", "message", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tkinter.messagebox.askretrycancel", "name": "askretrycancel", "type": {".class": "CallableType", "arg_kinds": [1, 1, 4], "arg_names": ["title", "message", "options"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "askretrycancel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "askyesno": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 4], "arg_names": ["title", "message", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tkinter.messagebox.askyesno", "name": "<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [1, 1, 4], "arg_names": ["title", "message", "options"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON>", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "askyesnocancel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 4], "arg_names": ["title", "message", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tkinter.messagebox.askyesnocancel", "name": "askyesnocancel", "type": {".class": "CallableType", "arg_kinds": [1, 1, 4], "arg_names": ["title", "message", "options"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "askyesnocancel", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "showerror": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 4], "arg_names": ["title", "message", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tkinter.messagebox.showerror", "name": "showerror", "type": {".class": "CallableType", "arg_kinds": [1, 1, 4], "arg_names": ["title", "message", "options"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "showerror", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "showinfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 4], "arg_names": ["title", "message", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tkinter.messagebox.showinfo", "name": "showinfo", "type": {".class": "CallableType", "arg_kinds": [1, 1, 4], "arg_names": ["title", "message", "options"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "showinfo", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "showwarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 4], "arg_names": ["title", "message", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tkinter.messagebox.showwarning", "name": "showwarning", "type": {".class": "CallableType", "arg_kinds": [1, 1, 4], "arg_names": ["title", "message", "options"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "showwarning", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\tkinter\\messagebox.pyi"}