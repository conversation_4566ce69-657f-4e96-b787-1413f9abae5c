"""
模块测试脚本
测试各个模块的基本功能
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from modules.screen_capture import ScreenCapture
        print("✓ 屏幕截图模块导入成功")
    except Exception as e:
        print(f"✗ 屏幕截图模块导入失败: {e}")
        return False
    
    try:
        from modules.image_recognition import ImageRecognition
        print("✓ 图像识别模块导入成功")
    except Exception as e:
        print(f"✗ 图像识别模块导入失败: {e}")
        return False
    
    try:
        from modules.path_finder import PathFinder
        print("✓ 路径查找模块导入成功")
    except Exception as e:
        print(f"✗ 路径查找模块导入失败: {e}")
        return False
    
    try:
        from modules.auto_clicker import AutoClicker
        print("✓ 自动点击模块导入成功")
    except Exception as e:
        print(f"✗ 自动点击模块导入失败: {e}")
        return False
    
    try:
        from utils.helpers import load_config
        print("✓ 工具函数模块导入成功")
    except Exception as e:
        print(f"✗ 工具函数模块导入失败: {e}")
        return False
    
    return True

def test_screen_capture():
    """测试屏幕截图功能"""
    print("\n测试屏幕截图功能...")
    
    try:
        from modules.screen_capture import ScreenCapture
        
        capture = ScreenCapture()
        
        # 测试截图
        screenshot = capture.capture_screen()
        if screenshot:
            print(f"✓ 屏幕截图成功，尺寸: {screenshot.size}")
        else:
            print("✗ 屏幕截图失败")
            return False
        
        # 测试网格计算
        capture.grid_size = (8, 12)
        cell_size = capture.calculate_grid(screenshot)
        if cell_size:
            print(f"✓ 网格计算成功，单元格大小: {cell_size}")
        else:
            print("✗ 网格计算失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 屏幕截图测试失败: {e}")
        return False

def test_image_recognition():
    """测试图像识别功能"""
    print("\n测试图像识别功能...")
    
    try:
        from modules.image_recognition import ImageRecognition
        from PIL import Image
        import numpy as np
        
        recognizer = ImageRecognition()
        
        # 创建测试图像
        test_image = Image.new('RGB', (64, 64), color='red')
        
        # 测试特征提取
        features = recognizer.extract_features(test_image)
        if features:
            print("✓ 图像特征提取成功")
        else:
            print("✗ 图像特征提取失败")
            return False
        
        # 测试空单元格检测
        empty_image = Image.new('RGB', (64, 64), color='white')
        is_empty = recognizer.is_empty_cell(empty_image)
        print(f"✓ 空单元格检测: {is_empty}")
        
        # 测试图像比较
        similarity = recognizer.compare_images(test_image, test_image)
        print(f"✓ 图像相似度比较: {similarity:.2f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 图像识别测试失败: {e}")
        return False

def test_path_finder():
    """测试路径查找功能"""
    print("\n测试路径查找功能...")
    
    try:
        from modules.path_finder import PathFinder
        
        # 创建测试网格
        test_grid = [
            ['A', 'B', 'A', 'empty'],
            ['empty', 'C', 'B', 'C'],
            ['D', 'empty', 'empty', 'D'],
            ['empty', 'empty', 'empty', 'empty']
        ]
        
        finder = PathFinder()
        finder.set_grid(test_grid)
        
        # 测试直线连接
        result = finder.can_connect((0, 0), (0, 2))
        print(f"✓ 直线连接测试: {result['can_connect']}")
        
        # 测试一个拐角连接
        result = finder.can_connect((2, 0), (2, 3))
        print(f"✓ 拐角连接测试: {result['can_connect']}")
        
        # 测试查找所有匹配
        matches = finder.find_all_matches()
        print(f"✓ 找到 {len(matches)} 个可连接的匹配")
        
        # 测试统计信息
        stats = finder.get_grid_statistics()
        print(f"✓ 网格统计: {stats}")
        
        return True
        
    except Exception as e:
        print(f"✗ 路径查找测试失败: {e}")
        return False

def test_auto_clicker():
    """测试自动点击功能"""
    print("\n测试自动点击功能...")
    
    try:
        from modules.auto_clicker import AutoClicker
        
        clicker = AutoClicker()
        
        # 测试配置
        clicker.set_click_delay(0.1)
        clicker.set_move_duration(0.1)
        clicker.enable_human_like_behavior(False)
        
        # 获取当前鼠标位置
        pos = clicker.get_mouse_position()
        print(f"✓ 当前鼠标位置: {pos}")
        
        # 测试坐标验证
        valid = clicker.validate_click_area(100, 100)
        print(f"✓ 坐标验证: {valid}")
        
        # 获取统计信息
        stats = clicker.get_click_statistics()
        print(f"✓ 点击器统计: {stats}")
        
        return True
        
    except Exception as e:
        print(f"✗ 自动点击测试失败: {e}")
        return False

def test_helpers():
    """测试工具函数"""
    print("\n测试工具函数...")
    
    try:
        from utils.helpers import (load_config, calculate_distance, format_time,
                                  get_screen_info, create_performance_monitor,
                                  update_performance_monitor, get_performance_stats)
        
        # 测试配置加载
        config = load_config()
        print(f"✓ 配置加载成功: {len(config)} 个配置项")
        
        # 测试距离计算
        distance = calculate_distance((0, 0), (3, 4))
        print(f"✓ 距离计算: {distance}")
        
        # 测试时间格式化
        time_str = format_time(125)
        print(f"✓ 时间格式化: {time_str}")
        
        # 测试屏幕信息
        screen_info = get_screen_info()
        print(f"✓ 屏幕信息: {screen_info}")
        
        # 测试性能监控
        monitor = create_performance_monitor()
        update_performance_monitor(monitor, True, 0.1)
        stats = get_performance_stats(monitor)
        print(f"✓ 性能监控: {stats}")
        
        return True
        
    except Exception as e:
        print(f"✗ 工具函数测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("连连看辅助工具 - 模块测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("屏幕截图", test_screen_capture),
        ("图像识别", test_image_recognition),
        ("路径查找", test_path_finder),
        ("自动点击", test_auto_clicker),
        ("工具函数", test_helpers),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n{test_name} 测试: ✓ 通过")
            else:
                print(f"\n{test_name} 测试: ✗ 失败")
        except Exception as e:
            print(f"\n{test_name} 测试: ✗ 异常 - {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！连连看辅助工具准备就绪。")
    else:
        print("⚠️  部分测试失败，请检查相关模块。")
    
    return passed == total

if __name__ == "__main__":
    main()
