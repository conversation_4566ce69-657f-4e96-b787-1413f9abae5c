{"data_mtime": 1751812742, "dep_lines": [6, 7, 8, 9, 10, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 10, 5, 30, 30, 30], "dependencies": ["json", "os", "time", "typing", "logging", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "bcc5f690ce62508911e0ada28f0bc8d65ac95792", "id": "utils.helpers", "ignore_all": true, "interface_hash": "ff4cf2c4d41238504a2edd8368c2b0fb0e019de2", "mtime": 1751812658, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\db\\0617\\连连看辅助工具\\utils\\helpers.py", "plugin_data": null, "size": 8907, "suppressed": [], "version_id": "1.15.0"}