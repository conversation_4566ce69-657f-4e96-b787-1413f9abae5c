{".class": "MypyFile", "_fullname": "tkinter.constants", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ACTIVE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "active", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.ACTIVE", "name": "ACTIVE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "active"}, "type_ref": "builtins.str"}}}, "ALL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "all", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.ALL", "name": "ALL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "all"}, "type_ref": "builtins.str"}}}, "ANCHOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "anchor", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.ANCHOR", "name": "ANCHOR", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "anchor"}, "type_ref": "builtins.str"}}}, "ARC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "arc", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.ARC", "name": "ARC", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "arc"}, "type_ref": "builtins.str"}}}, "BASELINE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "baseline", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.BASELINE", "name": "BASELINE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "baseline"}, "type_ref": "builtins.str"}}}, "BEVEL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "bevel", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.BEVEL", "name": "BEVEL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "bevel"}, "type_ref": "builtins.str"}}}, "BOTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "both", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.BOTH", "name": "BOTH", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "both"}, "type_ref": "builtins.str"}}}, "BOTTOM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "bottom", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.BOTTOM", "name": "BOTTOM", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "bottom"}, "type_ref": "builtins.str"}}}, "BROWSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "browse", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.BROWSE", "name": "BROWSE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "browse"}, "type_ref": "builtins.str"}}}, "BUTT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "butt", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.BUTT", "name": "BUTT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "butt"}, "type_ref": "builtins.str"}}}, "CASCADE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "cascade", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.CASCADE", "name": "CASCADE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "cascade"}, "type_ref": "builtins.str"}}}, "CENTER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "center", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.CENTER", "name": "CENTER", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, "type_ref": "builtins.str"}}}, "CHAR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "char", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.CHAR", "name": "CHAR", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "char"}, "type_ref": "builtins.str"}}}, "CHECKBUTTON": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "checkbutton", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.CHECKBUTTON", "name": "CHECKBUTTON", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "checkbutton"}, "type_ref": "builtins.str"}}}, "CHORD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "chord", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.CHORD", "name": "CHORD", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "chord"}, "type_ref": "builtins.str"}}}, "COMMAND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "command", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.COMMAND", "name": "COMMAND", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "command"}, "type_ref": "builtins.str"}}}, "CURRENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "current", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.CURRENT", "name": "CURRENT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "current"}, "type_ref": "builtins.str"}}}, "DISABLED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "disabled", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.DISABLED", "name": "DISABLED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "disabled"}, "type_ref": "builtins.str"}}}, "DOTBOX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "dotbox", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.DOTBOX", "name": "DOTBOX", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "dotbox"}, "type_ref": "builtins.str"}}}, "E": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "e", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.E", "name": "E", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "e"}, "type_ref": "builtins.str"}}}, "END": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "end", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.END", "name": "END", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "end"}, "type_ref": "builtins.str"}}}, "EW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "ew", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.EW", "name": "EW", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "ew"}, "type_ref": "builtins.str"}}}, "EXTENDED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "extended", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.EXTENDED", "name": "EXTENDED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "extended"}, "type_ref": "builtins.str"}}}, "FALSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "tkinter.constants.FALSE", "name": "FALSE", "type": "builtins.bool"}}, "FIRST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "first", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.FIRST", "name": "FIRST", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "first"}, "type_ref": "builtins.str"}}}, "FLAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "flat", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.FLAT", "name": "FLAT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "flat"}, "type_ref": "builtins.str"}}}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "GROOVE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "groove", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.GROOVE", "name": "GROOVE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "groove"}, "type_ref": "builtins.str"}}}, "HIDDEN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "hidden", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.HIDDEN", "name": "HIDDEN", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "hidden"}, "type_ref": "builtins.str"}}}, "HORIZONTAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "horizontal", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.HORIZONTAL", "name": "HORIZONTAL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "horizontal"}, "type_ref": "builtins.str"}}}, "INSERT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "insert", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.INSERT", "name": "INSERT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "insert"}, "type_ref": "builtins.str"}}}, "INSIDE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "inside", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.INSIDE", "name": "INSIDE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "inside"}, "type_ref": "builtins.str"}}}, "LAST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "last", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.LAST", "name": "LAST", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "last"}, "type_ref": "builtins.str"}}}, "LEFT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "left", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.LEFT", "name": "LEFT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "left"}, "type_ref": "builtins.str"}}}, "MITER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "miter", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.MITER", "name": "MITER", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "miter"}, "type_ref": "builtins.str"}}}, "MOVETO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "moveto", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.MOVETO", "name": "MOVETO", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "moveto"}, "type_ref": "builtins.str"}}}, "MULTIPLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "multiple", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.MULTIPLE", "name": "MULTIPLE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "multiple"}, "type_ref": "builtins.str"}}}, "N": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "n", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.N", "name": "N", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "n"}, "type_ref": "builtins.str"}}}, "NE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "ne", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.NE", "name": "NE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "ne"}, "type_ref": "builtins.str"}}}, "NO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "tkinter.constants.NO", "name": "NO", "type": "builtins.bool"}}, "NONE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "none", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.NONE", "name": "NONE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "none"}, "type_ref": "builtins.str"}}}, "NORMAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "normal", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.NORMAL", "name": "NORMAL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "normal"}, "type_ref": "builtins.str"}}}, "NS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "ns", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.NS", "name": "NS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "ns"}, "type_ref": "builtins.str"}}}, "NSEW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "nsew", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.NSEW", "name": "NSEW", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "nsew"}, "type_ref": "builtins.str"}}}, "NUMERIC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "numeric", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.NUMERIC", "name": "NUMERIC", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "numeric"}, "type_ref": "builtins.str"}}}, "NW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "nw", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.NW", "name": "NW", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "nw"}, "type_ref": "builtins.str"}}}, "OFF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "tkinter.constants.OFF", "name": "OFF", "type": "builtins.bool"}}, "ON": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "tkinter.constants.ON", "name": "ON", "type": "builtins.bool"}}, "OUTSIDE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "outside", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.OUTSIDE", "name": "OUTSIDE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "outside"}, "type_ref": "builtins.str"}}}, "PAGES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "pages", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.PAGES", "name": "PAGES", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "pages"}, "type_ref": "builtins.str"}}}, "PIESLICE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "pieslice", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.PIESLICE", "name": "PIESLICE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "pieslice"}, "type_ref": "builtins.str"}}}, "PROJECTING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "projecting", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.PROJECTING", "name": "PROJECTING", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "projecting"}, "type_ref": "builtins.str"}}}, "RADIOBUTTON": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "radiobutton", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.RADIOBUTTON", "name": "RADIOBUTTON", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "radiobutton"}, "type_ref": "builtins.str"}}}, "RAISED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "raised", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.RAISED", "name": "RAISED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "raised"}, "type_ref": "builtins.str"}}}, "RIDGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "ridge", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.RIDGE", "name": "RIDGE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "ridge"}, "type_ref": "builtins.str"}}}, "RIGHT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "right", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.RIGHT", "name": "RIGHT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "right"}, "type_ref": "builtins.str"}}}, "ROUND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "round", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.ROUND", "name": "ROUND", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "round"}, "type_ref": "builtins.str"}}}, "S": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "s", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.S", "name": "S", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "s"}, "type_ref": "builtins.str"}}}, "SCROLL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "scroll", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.SCROLL", "name": "SCROLL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "scroll"}, "type_ref": "builtins.str"}}}, "SE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "se", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.SE", "name": "SE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "se"}, "type_ref": "builtins.str"}}}, "SEL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "sel", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.SEL", "name": "SEL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "sel"}, "type_ref": "builtins.str"}}}, "SEL_FIRST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "sel.first", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.SEL_FIRST", "name": "SEL_FIRST", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "sel.first"}, "type_ref": "builtins.str"}}}, "SEL_LAST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "sel.last", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.SEL_LAST", "name": "SEL_LAST", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "sel.last"}, "type_ref": "builtins.str"}}}, "SEPARATOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "separator", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.SEPARATOR", "name": "SEPARATOR", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "separator"}, "type_ref": "builtins.str"}}}, "SINGLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "single", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.SINGLE", "name": "SINGLE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "single"}, "type_ref": "builtins.str"}}}, "SOLID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "solid", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.SOLID", "name": "SOLID", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "solid"}, "type_ref": "builtins.str"}}}, "SUNKEN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "sunken", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.SUNKEN", "name": "SUNKEN", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "sunken"}, "type_ref": "builtins.str"}}}, "SW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "sw", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.SW", "name": "SW", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "sw"}, "type_ref": "builtins.str"}}}, "TOP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "top", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.TOP", "name": "TOP", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "top"}, "type_ref": "builtins.str"}}}, "TRUE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "tkinter.constants.TRUE", "name": "TRUE", "type": "builtins.bool"}}, "UNDERLINE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "underline", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.UNDERLINE", "name": "UNDERLINE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "underline"}, "type_ref": "builtins.str"}}}, "UNITS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "units", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.UNITS", "name": "UNITS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "units"}, "type_ref": "builtins.str"}}}, "VERTICAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "vertical", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.VERTICAL", "name": "VERTICAL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "vertical"}, "type_ref": "builtins.str"}}}, "W": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "w", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.W", "name": "W", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, "type_ref": "builtins.str"}}}, "WORD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "word", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.WORD", "name": "WORD", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "word"}, "type_ref": "builtins.str"}}}, "X": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "x", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.X", "name": "X", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "x"}, "type_ref": "builtins.str"}}}, "Y": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "y", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tkinter.constants.Y", "name": "Y", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "y"}, "type_ref": "builtins.str"}}}, "YES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "tkinter.constants.YES", "name": "YES", "type": "builtins.bool"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tkinter.constants.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tkinter.constants.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tkinter.constants.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tkinter.constants.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tkinter.constants.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tkinter.constants.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\tkinter\\constants.pyi"}