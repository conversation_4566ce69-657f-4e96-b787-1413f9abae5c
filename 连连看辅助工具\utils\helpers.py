"""
辅助工具函数模块
提供各种实用的辅助函数
"""

import json
import os
import time
from typing import Dict, Any, List, Tuple
import logging


def setup_logging(log_file="game_assistant.log", level=logging.INFO):
    """
    设置日志记录
    
    Args:
        log_file: 日志文件名
        level: 日志级别
    """
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )


def load_config(config_file="config.json"):
    """
    加载配置文件
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        dict: 配置字典
    """
    default_config = {
        "click_delay": 0.3,
        "move_duration": 0.1,
        "grid_rows": 12,
        "grid_cols": 8,
        "similarity_threshold": 0.8,
        "enable_randomization": True,
        "position_variance": 3,
        "timing_variance": 0.05,
        "auto_detect_region": True,
        "save_debug_images": False
    }
    
    try:
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # 合并默认配置
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
        else:
            return default_config
    except Exception as e:
        logging.error(f"加载配置文件失败: {e}")
        return default_config


def save_config(config, config_file="config.json"):
    """
    保存配置文件
    
    Args:
        config: 配置字典
        config_file: 配置文件路径
    """
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)
        logging.info(f"配置已保存到 {config_file}")
    except Exception as e:
        logging.error(f"保存配置文件失败: {e}")


def calculate_distance(pos1, pos2):
    """
    计算两点间的距离
    
    Args:
        pos1, pos2: 位置元组 (x, y)
        
    Returns:
        float: 距离
    """
    return ((pos1[0] - pos2[0]) ** 2 + (pos1[1] - pos2[1]) ** 2) ** 0.5


def format_time(seconds):
    """
    格式化时间显示
    
    Args:
        seconds: 秒数
        
    Returns:
        str: 格式化的时间字符串
    """
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = seconds // 60
        secs = seconds % 60
        return f"{int(minutes)}分{int(secs)}秒"
    else:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        return f"{int(hours)}小时{int(minutes)}分"


def validate_grid_position(row, col, grid_rows, grid_cols):
    """
    验证网格位置是否有效
    
    Args:
        row, col: 行列坐标
        grid_rows, grid_cols: 网格尺寸
        
    Returns:
        bool: 位置是否有效
    """
    return 0 <= row < grid_rows and 0 <= col < grid_cols


def create_debug_directory():
    """
    创建调试目录
    
    Returns:
        str: 调试目录路径
    """
    debug_dir = "debug_images"
    if not os.path.exists(debug_dir):
        os.makedirs(debug_dir)
        logging.info(f"创建调试目录: {debug_dir}")
    return debug_dir


def cleanup_old_files(directory, max_age_days=7):
    """
    清理旧文件
    
    Args:
        directory: 目录路径
        max_age_days: 最大保留天数
    """
    try:
        current_time = time.time()
        max_age_seconds = max_age_days * 24 * 3600
        
        for filename in os.listdir(directory):
            file_path = os.path.join(directory, filename)
            if os.path.isfile(file_path):
                file_age = current_time - os.path.getmtime(file_path)
                if file_age > max_age_seconds:
                    os.remove(file_path)
                    logging.info(f"删除旧文件: {filename}")
    except Exception as e:
        logging.error(f"清理旧文件失败: {e}")


def get_screen_info():
    """
    获取屏幕信息
    
    Returns:
        dict: 屏幕信息
    """
    try:
        import pyautogui
        width, height = pyautogui.size()
        return {
            "width": width,
            "height": height,
            "aspect_ratio": width / height
        }
    except Exception as e:
        logging.error(f"获取屏幕信息失败: {e}")
        return {"width": 1920, "height": 1080, "aspect_ratio": 16/9}


def safe_divide(a, b, default=0):
    """
    安全除法
    
    Args:
        a, b: 被除数和除数
        default: 除零时的默认值
        
    Returns:
        float: 除法结果
    """
    try:
        return a / b if b != 0 else default
    except:
        return default


def clamp(value, min_value, max_value):
    """
    限制数值范围
    
    Args:
        value: 输入值
        min_value, max_value: 最小值和最大值
        
    Returns:
        数值: 限制后的值
    """
    return max(min_value, min(value, max_value))


def generate_timestamp():
    """
    生成时间戳字符串
    
    Returns:
        str: 时间戳
    """
    return time.strftime("%Y%m%d_%H%M%S")


def parse_region_string(region_str):
    """
    解析区域字符串
    
    Args:
        region_str: 区域字符串，格式如 "100,200,800,600"
        
    Returns:
        tuple: (x, y, width, height) 或 None
    """
    try:
        parts = region_str.split(',')
        if len(parts) == 4:
            return tuple(map(int, parts))
    except:
        pass
    return None


def format_region(region):
    """
    格式化区域信息
    
    Args:
        region: 区域元组 (x, y, width, height)
        
    Returns:
        str: 格式化的区域字符串
    """
    if region and len(region) == 4:
        return f"({region[0]}, {region[1]}) {region[2]}x{region[3]}"
    return "未设置"


def calculate_grid_metrics(region, grid_rows, grid_cols):
    """
    计算网格度量信息
    
    Args:
        region: 区域元组 (x, y, width, height)
        grid_rows, grid_cols: 网格行列数
        
    Returns:
        dict: 网格度量信息
    """
    if not region or len(region) != 4:
        return {}
    
    x, y, width, height = region
    cell_width = width / grid_cols
    cell_height = height / grid_rows
    
    return {
        "cell_width": cell_width,
        "cell_height": cell_height,
        "total_cells": grid_rows * grid_cols,
        "region_area": width * height,
        "cell_area": cell_width * cell_height
    }


def create_performance_monitor():
    """
    创建性能监控器
    
    Returns:
        dict: 性能监控器
    """
    return {
        "start_time": time.time(),
        "operations": 0,
        "successful_operations": 0,
        "failed_operations": 0,
        "total_processing_time": 0.0
    }


def update_performance_monitor(monitor, success=True, processing_time=0.0):
    """
    更新性能监控器
    
    Args:
        monitor: 性能监控器
        success: 操作是否成功
        processing_time: 处理时间
    """
    monitor["operations"] += 1
    monitor["total_processing_time"] += processing_time
    
    if success:
        monitor["successful_operations"] += 1
    else:
        monitor["failed_operations"] += 1


def get_performance_stats(monitor):
    """
    获取性能统计信息
    
    Args:
        monitor: 性能监控器
        
    Returns:
        dict: 性能统计
    """
    current_time = time.time()
    runtime = current_time - monitor["start_time"]
    
    return {
        "runtime": runtime,
        "total_operations": monitor["operations"],
        "successful_operations": monitor["successful_operations"],
        "failed_operations": monitor["failed_operations"],
        "success_rate": safe_divide(monitor["successful_operations"], monitor["operations"]),
        "operations_per_second": safe_divide(monitor["operations"], runtime),
        "average_processing_time": safe_divide(monitor["total_processing_time"], monitor["operations"])
    }


# 测试代码
if __name__ == "__main__":
    # 测试辅助函数
    print("辅助函数模块测试...")
    
    # 测试配置加载
    config = load_config()
    print(f"默认配置: {config}")
    
    # 测试屏幕信息
    screen_info = get_screen_info()
    print(f"屏幕信息: {screen_info}")
    
    # 测试性能监控
    monitor = create_performance_monitor()
    update_performance_monitor(monitor, True, 0.1)
    update_performance_monitor(monitor, False, 0.2)
    stats = get_performance_stats(monitor)
    print(f"性能统计: {stats}")
    
    print("辅助函数模块测试完成")
