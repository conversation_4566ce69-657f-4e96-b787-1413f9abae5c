{".class": "MypyFile", "_fullname": "modules.auto_clicker", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AutoClicker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "modules.auto_clicker.AutoClicker", "name": "AutoClicker", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "modules.auto_clicker.AutoClicker", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "modules.auto_clicker", "mro": ["modules.auto_clicker.AutoClicker", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "modules.auto_clicker.AutoClicker.__init__", "name": "__init__", "type": null}}, "_add_randomization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "modules.auto_clicker.AutoClicker._add_randomization", "name": "_add_randomization", "type": null}}, "_add_timing_randomization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "base_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "modules.auto_clicker.AutoClicker._add_timing_randomization", "name": "_add_timing_randomization", "type": null}}, "click_delay": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "modules.auto_clicker.AutoClicker.click_delay", "name": "click_delay", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "click_duration": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "modules.auto_clicker.AutoClicker.click_duration", "name": "click_duration", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "click_pair": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "pos1", "pos2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "modules.auto_clicker.AutoClicker.click_pair", "name": "click_pair", "type": null}}, "click_position": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "modules.auto_clicker.AutoClicker.click_position", "name": "click_position", "type": null}}, "click_sequence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "positions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "modules.auto_clicker.AutoClicker.click_sequence", "name": "click_sequence", "type": null}}, "emergency_stop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "modules.auto_clicker.AutoClicker.emergency_stop", "name": "emergency_stop", "type": null}}, "enable_human_like_behavior": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "enable"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "modules.auto_clicker.AutoClicker.enable_human_like_behavior", "name": "enable_human_like_behavior", "type": null}}, "enable_randomization": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "modules.auto_clicker.AutoClicker.enable_randomization", "name": "enable_randomization", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_click_statistics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "modules.auto_clicker.AutoClicker.get_click_statistics", "name": "get_click_statistics", "type": null}}, "get_mouse_position": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "modules.auto_clicker.AutoClicker.get_mouse_position", "name": "get_mouse_position", "type": null}}, "is_auto_running": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "modules.auto_clicker.AutoClicker.is_auto_running", "name": "is_auto_running", "type": null}}, "is_running": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "modules.auto_clicker.AutoClicker.is_running", "name": "is_running", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "move_duration": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "modules.auto_clicker.AutoClicker.move_duration", "name": "move_duration", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "position_variance": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "modules.auto_clicker.AutoClicker.position_variance", "name": "position_variance", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "set_click_delay": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "modules.auto_clicker.AutoClicker.set_click_delay", "name": "set_click_delay", "type": null}}, "set_move_duration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "duration"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "modules.auto_clicker.AutoClicker.set_move_duration", "name": "set_move_duration", "type": null}}, "simulate_human_pause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "modules.auto_clicker.AutoClicker.simulate_human_pause", "name": "simulate_human_pause", "type": null}}, "start_auto_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "modules.auto_clicker.AutoClicker.start_auto_mode", "name": "start_auto_mode", "type": null}}, "stop_auto_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "modules.auto_clicker.AutoClicker.stop_auto_mode", "name": "stop_auto_mode", "type": null}}, "stop_requested": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "modules.auto_clicker.AutoClicker.stop_requested", "name": "stop_requested", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "test_click_accuracy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "target_x", "target_y", "num_tests"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "modules.auto_clicker.AutoClicker.test_click_accuracy", "name": "test_click_accuracy", "type": null}}, "timing_variance": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "modules.auto_clicker.AutoClicker.timing_variance", "name": "timing_variance", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "validate_click_area": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "x", "y", "screen_width", "screen_height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "modules.auto_clicker.AutoClicker.validate_click_area", "name": "validate_click_area", "type": null}}, "wait_for_click_ready": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "modules.auto_clicker.AutoClicker.wait_for_click_ready", "name": "wait_for_click_ready", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "modules.auto_clicker.AutoClicker.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "modules.auto_clicker.AutoClicker", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "modules.auto_clicker.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "modules.auto_clicker.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "modules.auto_clicker.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "modules.auto_clicker.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "modules.auto_clicker.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "modules.auto_clicker.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "clicker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "modules.auto_clicker.clicker", "name": "clicker", "type": "modules.auto_clicker.AutoClicker"}}, "current_pos": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "modules.auto_clicker.current_pos", "name": "current_pos", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "modules.auto_clicker.key", "name": "key", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "pyautogui": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "modules.auto_clicker.pyautogui", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": {".class": "AnyType", "missing_import_name": "modules.auto_clicker.pyautogui", "source_any": null, "type_of_any": 3}}}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef"}, "stats": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "modules.auto_clicker.stats", "name": "stats", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "value": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "modules.auto_clicker.value", "name": "value", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "path": "E:\\db\\0617\\连连看辅助工具\\modules\\auto_clicker.py"}