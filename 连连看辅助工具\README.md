# 连连看游戏辅助工具

这是一个用于辅助连连看游戏的自动化工具，能够识别游戏界面上的图案并自动执行消除操作。

## 功能特性

- 🎯 自动识别游戏界面上的图案
- 🔍 智能路径查找（支持直线和一个拐角连接）
- 🖱️ 自动鼠标操作
- 🎮 简单易用的图形界面
- ⚡ 快速响应和高准确率

## 安装说明

1. 确保您的系统已安装 Python 3.8 或更高版本
2. 安装依赖包：
   ```bash
   pip install -r requirements.txt
   ```

## 使用方法

1. 运行主程序：
   ```bash
   python main.py
   ```
2. 打开连连看游戏
3. 在辅助工具中点击"开始"按钮
4. 工具将自动识别并消除可连接的图案

## 注意事项

- 请确保游戏窗口完全可见且未被遮挡
- 建议在1920x1080分辨率下使用以获得最佳效果
- 使用前请先备份游戏进度

## 项目结构

```
连连看辅助工具/
├── main.py              # 主程序入口
├── modules/              # 核心模块
│   ├── screen_capture.py    # 屏幕截图模块
│   ├── image_recognition.py # 图案识别模块
│   ├── path_finder.py       # 路径查找算法
│   └── auto_clicker.py      # 自动点击模块
├── gui/                  # 用户界面
│   └── main_window.py       # 主窗口
├── utils/                # 工具函数
│   └── helpers.py           # 辅助函数
└── requirements.txt      # 依赖包列表
```

## 免责声明

本工具仅供学习和研究使用，请遵守游戏的使用条款和相关法律法规。
