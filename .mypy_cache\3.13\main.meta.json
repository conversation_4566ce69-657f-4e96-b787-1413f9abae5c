{"data_mtime": 1751812793, "dep_lines": [16, 17, 18, 19, 20, 21, 6, 7, 8, 9, 10, 293, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 20, 5, 20, 20, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["modules.screen_capture", "modules.image_recognition", "modules.path_finder", "modules.auto_clicker", "gui.main_window", "utils.helpers", "sys", "os", "time", "threading", "typing", "random", "builtins", "logging", "json", "_frozen_importlib", "_typeshed", "abc", "gui", "modules", "types", "utils"], "hash": "91983be10fc44edf44c80f3770c2882ae34bff84", "id": "main", "ignore_all": false, "interface_hash": "adece1388171674e86a3cb256873f7236d0c12b7", "mtime": 1751812791, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\db\\0617\\连连看辅助工具\\main.py", "plugin_data": null, "size": 12328, "suppressed": [], "version_id": "1.15.0"}