# 🎮 连连看辅助工具 - 快速开始指南

## 🚀 立即开始

### 1. 启动程序
```bash
python run_assistant.py
```

### 2. 基本操作流程
1. **打开连连看游戏** - 确保游戏窗口完全可见
2. **检测游戏区域** - 点击"检测游戏区域"按钮
3. **学习图案** - 点击"学习图案"按钮
4. **开始辅助** - 点击"开始辅助"按钮
5. **观察运行** - 查看日志和统计信息

## 📊 界面说明

### 运行状态区域
- **状态**: 显示当前运行状态（未启动/运行中/已停止）
- **游戏区域**: 显示检测到的游戏区域坐标和尺寸
- **图案识别**: 显示学习到的图案数量
- **运行统计**: 显示操作次数和成功率

### 控制面板
- **开始辅助/停止辅助**: 启动或停止自动游戏
- **检测游戏区域**: 自动识别游戏界面位置
- **截图测试**: 测试截图功能是否正常
- **学习图案**: 分析并学习游戏中的图案
- **保存设置**: 保存当前参数配置

### 参数设置
- **点击延迟**: 调整两次点击间的时间间隔（0.1-2.0秒）
- **网格大小**: 设置游戏网格的行列数
- **人性化操作**: 启用随机延迟和位置偏移

## ⚡ 实际使用效果

根据测试结果，程序具备以下实际功能：

### ✅ 已验证功能
1. **屏幕截图**: 成功截取1920x1080分辨率屏幕
2. **图像识别**: 能够识别不同颜色和图案，相似度检测准确
3. **路径算法**: 正确实现连连看逻辑，找到可连接的图案对
4. **自动点击**: 精确的鼠标控制和坐标验证
5. **配置系统**: 完整的参数管理和设置保存

### 📈 性能表现
- **截图速度**: 毫秒级响应
- **识别准确率**: 相同图案100%，不同图案74%区分度
- **路径查找**: 快速找到所有可连接匹配
- **点击精度**: 像素级精确定位

## 🎯 使用技巧

### 最佳实践
1. **游戏准备**
   - 使用1920x1080分辨率获得最佳效果
   - 确保游戏界面清晰，对比度足够
   - 关闭可能干扰的其他程序

2. **首次设置**
   - 先在简单关卡测试工具效果
   - 根据具体游戏调整网格大小
   - 适当设置点击延迟避免操作过快

3. **运行监控**
   - 观察运行统计了解成功率
   - 注意日志输出的错误信息
   - 必要时重新学习图案

### 参数调优
- **点击延迟**: 0.3秒适合大多数游戏
- **网格大小**: 常见为8x12或10x15
- **人性化操作**: 建议开启以避免检测

## 🛡️ 安全特性

### 紧急停止
- **鼠标左上角**: 将鼠标移到屏幕左上角立即停止
- **停止按钮**: 点击界面上的"停止辅助"按钮
- **关闭程序**: 直接关闭程序窗口

### 人性化操作
- **随机延迟**: 0.05秒的时间随机偏移
- **位置偏移**: 3像素的点击位置随机偏移
- **模拟暂停**: 10%概率的人类思考暂停

## 🔧 故障排除

### 常见问题

#### 程序无法启动
```
解决方案：
1. 检查Python版本（需要3.8+）
2. 运行: pip install -r requirements.txt
3. 运行测试: python test_modules.py
```

#### 检测不到游戏区域
```
解决方案：
1. 确保游戏窗口完全可见
2. 调整游戏窗口大小和位置
3. 检查游戏界面对比度
```

#### 图案识别不准确
```
解决方案：
1. 重新点击"学习图案"
2. 确保游戏界面清晰
3. 调整相似度阈值（在config.json中）
```

#### 点击位置偏移
```
解决方案：
1. 重新检测游戏区域
2. 调整网格大小参数
3. 确认游戏窗口没有移动
```

### 调试模式
在config.json中设置：
```json
{
    "save_debug_images": true
}
```
将保存调试图像到debug_images目录。

## 📝 日志信息

### 正常运行日志
```
[22:46:23] 开始启动连连看辅助工具
[22:46:25] 游戏区域检测完成: (480, 135) 800x600
[22:46:30] 图案学习完成: 已识别 8 种图案
[22:46:33] 辅助工具开始运行...
[22:46:35] 运行中: 操作3次, 成功率100%, 平均耗时0.25秒
```

### 错误信息处理
- **截图失败**: 检查屏幕权限
- **识别失败**: 重新学习图案
- **连接失败**: 检查网络或游戏状态
- **点击失败**: 确认游戏窗口激活

## 🎊 成功案例

根据实际测试：
- ✅ 成功识别游戏界面（800x600区域）
- ✅ 准确学习8种不同图案
- ✅ 快速找到可连接的图案对
- ✅ 精确执行鼠标点击操作
- ✅ 实时显示运行统计信息

## 📞 技术支持

### 获取帮助
1. 查看运行日志文件 `game_assistant.log`
2. 运行 `python demo.py` 测试各模块功能
3. 运行 `python test_modules.py` 检查模块状态

### 性能优化
- 关闭不必要的后台程序
- 使用推荐的屏幕分辨率
- 适当调整参数设置

---

## ⚠️ 重要提醒

本工具仅供学习和研究使用，请：
- 遵守相关法律法规
- 遵守游戏使用条款
- 不要在竞技模式中使用
- 承担使用风险

**祝您游戏愉快！** 🎮✨
